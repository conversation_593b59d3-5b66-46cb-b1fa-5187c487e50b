"""
Test suite for database environment variables creation and management.

This test file is part of EPIC-AUDIT-001 - Database Configuration Overhaul
Task: Create Database Environment Variables
"""

import os
import sys
import unittest
from unittest.mock import patch, MagicMock
from pathlib import Path
import tempfile
import shutil

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

class DatabaseEnvironmentVariablesTest(unittest.TestCase):
    """
    Test database environment variables creation and management.
    """
    
    def setUp(self):
        """Set up test environment"""
        self.backend_dir = Path(__file__).parent
        self.env_file = self.backend_dir / '.env'
        self.original_env = dict(os.environ)
        
    def tearDown(self):
        """Clean up test environment"""
        # Restore original environment
        os.environ.clear()
        os.environ.update(self.original_env)
    
    def test_env_file_exists_and_readable(self):
        """
        Test that .env file exists and is readable
        """
        self.assertTrue(self.env_file.exists(), 
                       f".env file should exist at {self.env_file}")
        
        # Test file is readable
        with open(self.env_file, 'r') as f:
            content = f.read()
        
        self.assertIsInstance(content, str)
        self.assertGreater(len(content), 0)
        
        print(f"✅ .env file exists and is readable: {self.env_file}")
    
    def test_required_database_environment_variables_present(self):
        """
        Test that all required database environment variables are present in .env file
        """
        required_vars = [
            'DB_NAME',
            'DB_USER', 
            'DB_PASSWORD',
            'DB_HOST',
            'DB_PORT',
            'USE_SQLITE'
        ]
        
        with open(self.env_file, 'r') as f:
            env_content = f.read()
        
        missing_vars = []
        for var in required_vars:
            if var not in env_content:
                missing_vars.append(var)
        
        self.assertEqual(len(missing_vars), 0, 
                        f"Missing required environment variables: {missing_vars}")
        
        print("✅ All required database environment variables present")
        for var in required_vars:
            # Extract value from .env file
            for line in env_content.split('\n'):
                if line.startswith(f"{var}="):
                    value = line.split('=', 1)[1]
                    print(f"   {var}: {value}")
                    break
    
    def test_environment_variable_format_validation(self):
        """
        Test that environment variables are properly formatted
        """
        with open(self.env_file, 'r') as f:
            lines = f.readlines()
        
        format_errors = []
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            
            # Skip empty lines and comments
            if not line or line.startswith('#'):
                continue
            
            # Check format: VAR=value
            if '=' not in line:
                format_errors.append(f"Line {i}: Missing '=' separator: {line}")
                continue
            
            var_name, value = line.split('=', 1)
            
            # Validate variable name format
            if not var_name.replace('_', '').isalnum():
                format_errors.append(f"Line {i}: Invalid variable name format: {var_name}")
            
            # Check for database variables specifically
            if var_name.startswith('DB_'):
                if not value:
                    format_errors.append(f"Line {i}: Database variable {var_name} has empty value")
        
        self.assertEqual(len(format_errors), 0, 
                        f"Environment variable format errors: {format_errors}")
        
        print("✅ Environment variable format validation passed")
    
    def test_database_environment_variables_loaded_correctly(self):
        """
        Test that database environment variables are loaded correctly into Django
        """
        # Import Django settings to trigger environment loading
        from vierla_project.settings import base
        
        # Check that environment variables are loaded
        expected_vars = {
            'DB_NAME': 'vierla_db',
            'DB_USER': 'vierla_user', 
            'DB_PASSWORD': 'vierla_password',
            'DB_HOST': 'localhost',
            'DB_PORT': '5432',
            'USE_SQLITE': 'false'
        }
        
        print(f"\n=== DATABASE ENVIRONMENT VARIABLES LOADING TEST ===")
        
        all_loaded = True
        for var, expected_value in expected_vars.items():
            actual_value = os.environ.get(var)
            print(f"{var}: Expected='{expected_value}', Actual='{actual_value}'")
            
            if actual_value != expected_value:
                all_loaded = False
        
        self.assertTrue(all_loaded, "Not all database environment variables loaded correctly")
        print("✅ Database environment variables loaded correctly")
    
    def test_environment_variable_precedence(self):
        """
        Test that environment variables take precedence over .env file values
        """
        # Set an environment variable that should override .env
        test_var = 'DB_NAME'
        test_value = 'test_override_db'
        
        with patch.dict(os.environ, {test_var: test_value}):
            # Clear module cache to force reimport
            if 'vierla_project.settings.development' in sys.modules:
                del sys.modules['vierla_project.settings.development']
            
            from vierla_project.settings import development
            
            # Check that environment variable takes precedence
            db_config = development.DATABASES['default']
            
            if db_config['ENGINE'] == 'django.db.backends.postgresql':
                self.assertEqual(db_config['NAME'], test_value)
                print(f"✅ Environment variable precedence working: {test_var}={test_value}")
            else:
                print("ℹ️ System using SQLite - precedence test not applicable")
    
    def test_sensitive_environment_variables_security(self):
        """
        Test that sensitive environment variables are handled securely
        """
        sensitive_vars = ['DB_PASSWORD', 'SECRET_KEY']
        
        with open(self.env_file, 'r') as f:
            env_content = f.read()
        
        security_issues = []
        
        for var in sensitive_vars:
            for line in env_content.split('\n'):
                if line.startswith(f"{var}="):
                    value = line.split('=', 1)[1]
                    
                    # Check for common weak values
                    weak_values = ['password', '123456', 'admin', 'test', '']
                    if value.lower() in weak_values:
                        security_issues.append(f"{var} has weak value: {value}")
                    
                    # Check minimum length for passwords
                    if var == 'DB_PASSWORD' and len(value) < 8:
                        security_issues.append(f"{var} is too short: {len(value)} characters")
                    
                    break
        
        # For development environment, we allow weak passwords but warn
        if security_issues:
            print(f"⚠️ Security warnings (development environment): {security_issues}")
        else:
            print("✅ No obvious security issues detected")
    
    def test_environment_variable_backup_and_restore(self):
        """
        Test environment variable backup and restore functionality
        """
        # Create a backup of current .env file
        backup_file = self.env_file.with_suffix('.env.backup')
        
        try:
            shutil.copy2(self.env_file, backup_file)
            self.assertTrue(backup_file.exists(), "Backup file should be created")
            
            # Verify backup content matches original
            with open(self.env_file, 'r') as original:
                original_content = original.read()
            
            with open(backup_file, 'r') as backup:
                backup_content = backup.read()
            
            self.assertEqual(original_content, backup_content, 
                           "Backup content should match original")
            
            print("✅ Environment variable backup and restore functionality working")
            
        finally:
            # Clean up backup file
            if backup_file.exists():
                backup_file.unlink()
    
    def test_environment_variable_validation_rules(self):
        """
        Test validation rules for environment variables
        """
        validation_rules = {
            'DB_PORT': lambda x: x.isdigit() and 1 <= int(x) <= 65535,
            'DB_HOST': lambda x: len(x) > 0 and not x.isspace(),
            'DB_NAME': lambda x: len(x) > 0 and x.replace('_', '').isalnum(),
            'DB_USER': lambda x: len(x) > 0 and x.replace('_', '').isalnum(),
            'USE_SQLITE': lambda x: x.lower() in ['true', 'false']
        }
        
        with open(self.env_file, 'r') as f:
            env_content = f.read()
        
        validation_errors = []
        
        for var, rule in validation_rules.items():
            for line in env_content.split('\n'):
                if line.startswith(f"{var}="):
                    value = line.split('=', 1)[1]
                    
                    if not rule(value):
                        validation_errors.append(f"{var}={value} fails validation rule")
                    
                    break
        
        self.assertEqual(len(validation_errors), 0, 
                        f"Environment variable validation errors: {validation_errors}")
        
        print("✅ Environment variable validation rules passed")
    
    def test_environment_variable_documentation(self):
        """
        Test that environment variables are properly documented
        """
        # Check for comments or documentation in .env file
        with open(self.env_file, 'r') as f:
            lines = f.readlines()
        
        documented_vars = set()
        comment_lines = []
        
        for line in lines:
            line = line.strip()
            if line.startswith('#'):
                comment_lines.append(line)
            elif '=' in line:
                var_name = line.split('=')[0]
                documented_vars.add(var_name)
        
        # Check if there's some documentation
        has_documentation = len(comment_lines) > 0
        
        print(f"✅ Environment file documentation:")
        print(f"   Comment lines: {len(comment_lines)}")
        print(f"   Documented variables: {len(documented_vars)}")
        
        if has_documentation:
            print("   Sample comments:")
            for comment in comment_lines[:3]:  # Show first 3 comments
                print(f"     {comment}")
    
    def test_create_environment_template(self):
        """
        Test creation of environment template file
        """
        template_file = self.backend_dir / '.env.template'
        
        # Create template content
        template_content = """# Vierla Application Environment Variables Template
# Copy this file to .env and fill in the actual values

# Database Configuration
DB_NAME=vierla_db
DB_USER=vierla_user
DB_PASSWORD=your_secure_password_here
DB_HOST=localhost
DB_PORT=5432
DB_SSLMODE=prefer

# Application Configuration
SECRET_KEY=your_secret_key_here
DEBUG=True
USE_SQLITE=false
DJANGO_ENVIRONMENT=development

# Optional: Additional Database Settings
# DB_CONN_MAX_AGE=600
# DB_CONN_HEALTH_CHECKS=true
"""
        
        try:
            with open(template_file, 'w') as f:
                f.write(template_content)
            
            self.assertTrue(template_file.exists(), "Template file should be created")
            
            # Verify template content
            with open(template_file, 'r') as f:
                content = f.read()
            
            self.assertIn('DB_NAME', content)
            self.assertIn('DB_PASSWORD', content)
            self.assertIn('your_secure_password_here', content)
            
            print(f"✅ Environment template created: {template_file}")
            
        finally:
            # Clean up template file
            if template_file.exists():
                template_file.unlink()
    
    def test_environment_variable_migration_script(self):
        """
        Test environment variable migration script functionality
        """
        # Test upgrading environment variables with new required fields
        old_env_content = """DB_NAME=vierla_db
DB_USER=vierla_user
DB_PASSWORD=vierla_password
DB_HOST=localhost
DB_PORT=5432
"""
        
        new_required_vars = {
            'USE_SQLITE': 'false',
            'DB_SSLMODE': 'prefer',
            'DJANGO_ENVIRONMENT': 'development'
        }
        
        # Simulate migration
        migrated_content = old_env_content
        
        for var, default_value in new_required_vars.items():
            if var not in migrated_content:
                migrated_content += f"{var}={default_value}\n"
        
        # Verify migration
        for var in new_required_vars:
            self.assertIn(var, migrated_content)
        
        print("✅ Environment variable migration functionality working")
        print("   Added variables:")
        for var, value in new_required_vars.items():
            print(f"     {var}={value}")


class DatabaseEnvironmentVariablesIntegrationTest(unittest.TestCase):
    """
    Integration tests for database environment variables with Django settings
    """
    
    def test_environment_variables_django_integration(self):
        """
        Test that environment variables integrate correctly with Django settings
        """
        from django.conf import settings
        
        # Test that database configuration uses environment variables
        db_config = settings.DATABASES['default']
        
        # Check that configuration is environment-driven
        self.assertIsNotNone(db_config.get('NAME'))
        self.assertIsNotNone(db_config.get('USER'))
        self.assertIsNotNone(db_config.get('PASSWORD'))
        self.assertIsNotNone(db_config.get('HOST'))
        self.assertIsNotNone(db_config.get('PORT'))
        
        print("✅ Environment variables integrated with Django settings")
        print(f"   Database Engine: {db_config.get('ENGINE')}")
        print(f"   Database Name: {db_config.get('NAME')}")
        print(f"   Database Host: {db_config.get('HOST')}:{db_config.get('PORT')}")
    
    def test_environment_variables_with_different_environments(self):
        """
        Test environment variables work correctly in different environments
        """
        environments = ['development', 'testing', 'production']
        
        for env in environments:
            with patch.dict(os.environ, {'DJANGO_ENVIRONMENT': env}):
                # Clear module cache
                modules_to_clear = [
                    'vierla_project.settings',
                    f'vierla_project.settings.{env}'
                ]
                
                for module in modules_to_clear:
                    if module in sys.modules:
                        del sys.modules[module]
                
                try:
                    # Import environment-specific settings
                    settings_module = __import__(f'vierla_project.settings.{env}', fromlist=[''])
                    
                    # Verify settings loaded correctly
                    self.assertTrue(hasattr(settings_module, 'DATABASES'))
                    
                    print(f"✅ Environment variables work in {env} environment")
                    
                except ImportError:
                    print(f"ℹ️ {env} environment settings not found (expected for some environments)")


if __name__ == '__main__':
    # Run the database environment variables tests
    unittest.main(verbosity=2)
