/**
 * Service Management Screen
 * Provider screen for managing services
 */

import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export const ServiceManagementScreen: React.FC = () => {
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content}>
        <Text style={styles.title}>Service Management</Text>
        <Text style={styles.subtitle}>Manage your services and offerings</Text>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Service Overview</Text>
          <Text style={styles.text}>
            This is a placeholder for the service management screen. 
            This screen will allow providers to create, edit, and manage their services.
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Features</Text>
          <Text style={styles.text}>• Create and edit services</Text>
          <Text style={styles.text}>• Set pricing and availability</Text>
          <Text style={styles.text}>• Manage service categories</Text>
          <Text style={styles.text}>• Upload service images</Text>
          <Text style={styles.text}>• Service performance analytics</Text>
          <Text style={styles.text}>• Enable/disable services</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Current Services</Text>
          <View style={styles.serviceCard}>
            <Text style={styles.serviceTitle}>No services yet</Text>
            <Text style={styles.serviceDescription}>
              Start by creating your first service offering
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    marginBottom: 24,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 12,
  },
  text: {
    fontSize: 16,
    color: '#333333',
    lineHeight: 24,
    marginBottom: 8,
  },
  serviceCard: {
    backgroundColor: '#F8F9FA',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  serviceTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 4,
  },
  serviceDescription: {
    fontSize: 14,
    color: '#666666',
  },
});

export default ServiceManagementScreen;
