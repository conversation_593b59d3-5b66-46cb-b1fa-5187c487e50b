/**
 * Lazy Screen Components
 * Implements lazy loading for screens to improve app performance
 * Based on reference implementation patterns
 */

import React, { Suspense } from 'react';
import { View, Text, ActivityIndicator, StyleSheet } from 'react-native';

// Loading fallback component
const LoadingFallback: React.FC<{ screenName?: string }> = ({ screenName }) => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color="#007AFF" />
    <Text style={styles.loadingText}>
      Loading {screenName || 'Screen'}...
    </Text>
  </View>
);

// Error fallback component
const ErrorFallback: React.FC<{ screenName?: string; error?: Error }> = ({ 
  screenName, 
  error 
}) => (
  <View style={styles.errorContainer}>
    <Text style={styles.errorTitle}>Failed to load {screenName || 'Screen'}</Text>
    <Text style={styles.errorMessage}>
      {error?.message || 'An unexpected error occurred'}
    </Text>
  </View>
);

// Higher-order component for lazy loading
const withLazyLoading = (
  importFn: () => Promise<{ default: React.ComponentType<any> }>,
  screenName: string
) => {
  const LazyComponent = React.lazy(importFn);
  
  return React.forwardRef<any, any>((props, ref) => (
    <Suspense fallback={<LoadingFallback screenName={screenName} />}>
      <LazyComponent {...props} ref={ref} />
    </Suspense>
  ));
};

// Customer-specific lazy screens
export const LazyServiceDetailsScreen = withLazyLoading(
  () => import('../../screens/customer/ServiceDetailsScreen'),
  'Service Details'
);

export const LazyBookingDetailsScreen = withLazyLoading(
  () => import('../../screens/customer/BookingDetailsScreen'),
  'Booking Details'
);

export const LazyEditProfileScreen = withLazyLoading(
  () => import('../../screens/shared/EditProfileScreen'),
  'Edit Profile'
);

export const LazyNotificationsScreen = withLazyLoading(
  () => import('../../screens/shared/NotificationsScreen'),
  'Notifications'
);

// Provider-specific lazy screens
export const LazyProviderDashboardScreen = withLazyLoading(
  () => import('../../screens/provider/ProviderDashboardScreen'),
  'Provider Dashboard'
);

export const LazyServiceManagementScreen = withLazyLoading(
  () => import('../../screens/provider/ServiceManagementScreen'),
  'Service Management'
);

export const LazyBookingManagementScreen = withLazyLoading(
  () => import('../../screens/provider/BookingManagementScreen'),
  'Booking Management'
);

export const LazyProviderAnalyticsScreen = withLazyLoading(
  () => import('../../screens/provider/ProviderAnalyticsScreen'),
  'Provider Analytics'
);

export const LazyCustomerManagementScreen = withLazyLoading(
  () => import('../../screens/provider/CustomerManagementScreen'),
  'Customer Management'
);

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666666',
    fontWeight: '500',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding: 20,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FF3B30',
    marginBottom: 8,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 20,
  },
});
