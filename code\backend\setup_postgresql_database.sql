
-- PostgreSQL Database Setup Script for Vierla Application
-- Run this script as PostgreSQL superuser (postgres)

-- Create user if not exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'vierla_user') THEN
        CREATE USER vierla_user WITH PASSWORD 'vierla_password';
        RAISE NOTICE 'User vierla_user created';
    ELSE
        RAISE NOTICE 'User vierla_user already exists';
    END IF;
END
$$;

-- Create database if not exists
SELECT 'CREATE DATABASE vierla_db OWNER vierla_user'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'vierla_db')\gexec

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE vierla_db TO vierla_user;

-- Verify setup
\c vierla_db
SELECT 'Database setup completed successfully for vierla_db' as status;
