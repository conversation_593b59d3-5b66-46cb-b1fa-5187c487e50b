"""
API v1 URL Configuration
Implements versioned, role-based API architecture
"""

from django.urls import path, include
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status

@api_view(['GET'])
@permission_classes([AllowAny])
def api_v1_root(request):
    """API v1 root endpoint - public access"""
    return Response({
        'version': '1.0',
        'description': 'Vierla API v1 - Role-based architecture',
        'endpoints': {
            'customer': '/api/v1/customer/',
            'provider': '/api/v1/provider/',
            'shared': '/api/v1/shared/',
        },
        'documentation': '/api/docs/',
        'schema': '/api/schema/'
    })

urlpatterns = [
    # API v1 root
    path('', api_v1_root, name='api_v1_root'),
    
    # Role-based endpoints
    path('customer/', include('api.v1.customer.urls')),
    path('provider/', include('api.v1.provider.urls')),
    path('shared/', include('api.v1.shared.urls')),
]
