"""
Database Environment Setup Script

This script provides comprehensive database environment variable management
and PostgreSQL setup for the Vierla application.

Part of EPIC-AUDIT-001 - Database Configuration Overhaul
Task: Create Database Environment Variables
"""

import os
import sys
import subprocess
import psycopg2
from pathlib import Path
import shutil
from datetime import datetime

class DatabaseEnvironmentManager:
    """
    Manages database environment variables and PostgreSQL setup
    """
    
    def __init__(self):
        self.backend_dir = Path(__file__).parent
        self.env_file = self.backend_dir / '.env'
        self.env_template = self.backend_dir / '.env.template'
        self.env_backup = self.backend_dir / '.env.backup'
        
    def print_header(self, title):
        """Print a formatted header"""
        print(f"\n{'='*60}")
        print(f"{title}")
        print(f"{'='*60}")
    
    def print_step(self, step, description):
        """Print a formatted step"""
        print(f"\n{step}. {description}")
        print("-" * 50)
    
    def create_environment_template(self):
        """Create .env.template file with all required variables"""
        self.print_step(1, "Creating Environment Template")
        
        template_content = """# Vierla Application Environment Variables
# Copy this file to .env and customize the values for your environment

# Database Configuration
DB_NAME=vierla_db
DB_USER=vierla_user
DB_PASSWORD=vierla_password
DB_HOST=localhost
DB_PORT=5432
DB_SSLMODE=prefer

# Database Connection Optimization
DB_CONN_MAX_AGE=600
DB_CONN_HEALTH_CHECKS=true

# Application Configuration
SECRET_KEY=your-secret-key-here-change-in-production
DEBUG=True
USE_SQLITE=false
DJANGO_ENVIRONMENT=development

# Security Settings
ALLOWED_HOSTS=localhost,127.0.0.1
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Optional: Email Configuration (for production)
# EMAIL_HOST=smtp.gmail.com
# EMAIL_PORT=587
# EMAIL_USE_TLS=True
# EMAIL_HOST_USER=<EMAIL>
# EMAIL_HOST_PASSWORD=your-app-password

# Optional: Cloud Storage (for production)
# AWS_ACCESS_KEY_ID=your-aws-access-key
# AWS_SECRET_ACCESS_KEY=your-aws-secret-key
# AWS_STORAGE_BUCKET_NAME=your-bucket-name
"""
        
        with open(self.env_template, 'w') as f:
            f.write(template_content)
        
        print(f"✅ Environment template created: {self.env_template}")
        return True
    
    def backup_environment_file(self):
        """Create backup of existing .env file"""
        if self.env_file.exists():
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = self.backend_dir / f'.env.backup.{timestamp}'
            shutil.copy2(self.env_file, backup_file)
            print(f"✅ Environment file backed up: {backup_file}")
            return backup_file
        return None
    
    def validate_environment_variables(self):
        """Validate environment variables in .env file"""
        self.print_step(2, "Validating Environment Variables")
        
        if not self.env_file.exists():
            print(f"❌ .env file not found: {self.env_file}")
            return False
        
        required_vars = [
            'DB_NAME', 'DB_USER', 'DB_PASSWORD', 'DB_HOST', 'DB_PORT',
            'SECRET_KEY', 'DEBUG', 'USE_SQLITE'
        ]
        
        validation_rules = {
            'DB_PORT': lambda x: x.isdigit() and 1 <= int(x) <= 65535,
            'DB_HOST': lambda x: len(x) > 0 and not x.isspace(),
            'DB_NAME': lambda x: len(x) > 0 and x.replace('_', '').replace('-', '').isalnum(),
            'DB_USER': lambda x: len(x) > 0 and x.replace('_', '').replace('-', '').isalnum(),
            'USE_SQLITE': lambda x: x.lower() in ['true', 'false'],
            'DEBUG': lambda x: x.lower() in ['true', 'false']
        }
        
        with open(self.env_file, 'r') as f:
            env_content = f.read()
        
        # Check required variables
        missing_vars = []
        for var in required_vars:
            if f"{var}=" not in env_content:
                missing_vars.append(var)
        
        if missing_vars:
            print(f"❌ Missing required variables: {missing_vars}")
            return False
        
        # Validate format and rules
        validation_errors = []
        env_vars = {}
        
        for line in env_content.split('\n'):
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            
            if '=' not in line:
                validation_errors.append(f"Invalid format: {line}")
                continue
            
            var_name, value = line.split('=', 1)
            env_vars[var_name] = value
            
            # Apply validation rules
            if var_name in validation_rules:
                if not validation_rules[var_name](value):
                    validation_errors.append(f"{var_name}={value} fails validation")
        
        if validation_errors:
            print(f"❌ Validation errors: {validation_errors}")
            return False
        
        print("✅ Environment variables validation passed")
        print(f"   Found {len(env_vars)} variables")
        
        # Display key variables (without sensitive values)
        safe_vars = ['DB_NAME', 'DB_USER', 'DB_HOST', 'DB_PORT', 'USE_SQLITE', 'DEBUG']
        for var in safe_vars:
            if var in env_vars:
                print(f"   {var}: {env_vars[var]}")
        
        return True
    
    def test_postgresql_connection(self):
        """Test PostgreSQL connection with environment variables"""
        self.print_step(3, "Testing PostgreSQL Connection")
        
        # Load environment variables
        try:
            from dotenv import load_dotenv
            load_dotenv(self.env_file)
        except ImportError:
            print("❌ python-dotenv not installed")
            return False
        
        # Get database configuration
        db_config = {
            'host': os.environ.get('DB_HOST', 'localhost'),
            'port': os.environ.get('DB_PORT', '5432'),
            'user': os.environ.get('DB_USER', 'vierla_user'),
            'password': os.environ.get('DB_PASSWORD', 'vierla_password'),
            'database': os.environ.get('DB_NAME', 'vierla_db')
        }
        
        print(f"Testing connection to {db_config['host']}:{db_config['port']}")
        print(f"Database: {db_config['database']}, User: {db_config['user']}")
        
        try:
            conn = psycopg2.connect(
                host=db_config['host'],
                port=db_config['port'],
                user=db_config['user'],
                password=db_config['password'],
                database=db_config['database'],
                connect_timeout=5
            )
            
            cursor = conn.cursor()
            cursor.execute("SELECT version()")
            version = cursor.fetchone()[0]
            
            cursor.close()
            conn.close()
            
            print("✅ PostgreSQL connection successful")
            print(f"   Version: {version}")
            return True
            
        except Exception as e:
            print(f"❌ PostgreSQL connection failed: {e}")
            print("   This is expected if PostgreSQL is not set up yet")
            return False
    
    def generate_setup_instructions(self):
        """Generate setup instructions for PostgreSQL"""
        self.print_step(4, "Generating Setup Instructions")
        
        instructions = f"""
# PostgreSQL Setup Instructions for Vierla Application

## Prerequisites
1. PostgreSQL must be installed and running
2. You need superuser access to PostgreSQL

## Automatic Setup (Recommended)
Run the database setup script:
```bash
python setup_database_configuration.py
```

## Manual Setup
If automatic setup fails, follow these steps:

### 1. Connect to PostgreSQL as superuser
```bash
psql -U postgres
```

### 2. Create database and user
```sql
-- Create user
CREATE USER vierla_user WITH PASSWORD 'vierla_password';

-- Create database
CREATE DATABASE vierla_db OWNER vierla_user;

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE vierla_db TO vierla_user;

-- Exit PostgreSQL
\\q
```

### 3. Test connection
```bash
psql -U vierla_user -d vierla_db -h localhost
```

### 4. Update environment variables
Edit .env file if needed:
```
DB_NAME=vierla_db
DB_USER=vierla_user
DB_PASSWORD=vierla_password
DB_HOST=localhost
DB_PORT=5432
USE_SQLITE=false
```

### 5. Run Django migrations
```bash
python manage.py migrate
```

## Troubleshooting

### PostgreSQL Authentication Issues
If you get authentication errors:

1. Check PostgreSQL is running:
   ```bash
   # Windows
   net start postgresql-x64-17
   
   # Linux/Mac
   sudo systemctl status postgresql
   ```

2. Reset postgres user password:
   ```bash
   # Windows (as Administrator)
   psql -U postgres
   ALTER USER postgres PASSWORD 'postgres';
   
   # Linux/Mac
   sudo -u postgres psql
   ALTER USER postgres PASSWORD 'postgres';
   ```

3. Check pg_hba.conf for authentication method
   - Location: PostgreSQL data directory
   - Change 'md5' to 'trust' for local connections (development only)

### Connection Refused
- Verify PostgreSQL is running on correct port (5432)
- Check firewall settings
- Verify PostgreSQL accepts connections on localhost

### Database Does Not Exist
- Run the CREATE DATABASE command manually
- Verify user has correct permissions

## Environment Variables Reference

| Variable | Description | Default |
|----------|-------------|---------|
| DB_NAME | Database name | vierla_db |
| DB_USER | Database user | vierla_user |
| DB_PASSWORD | Database password | vierla_password |
| DB_HOST | Database host | localhost |
| DB_PORT | Database port | 5432 |
| DB_SSLMODE | SSL mode | prefer |
| USE_SQLITE | Use SQLite fallback | false |

## Security Notes
- Change default passwords in production
- Use environment-specific .env files
- Never commit .env files to version control
- Use strong passwords for production databases
"""
        
        instructions_file = self.backend_dir / 'POSTGRESQL_SETUP.md'
        with open(instructions_file, 'w') as f:
            f.write(instructions)
        
        print(f"✅ Setup instructions generated: {instructions_file}")
        return instructions_file
    
    def run_full_setup(self):
        """Run complete database environment setup"""
        self.print_header("VIERLA DATABASE ENVIRONMENT SETUP")
        
        print("This script will set up database environment variables and PostgreSQL configuration.")
        
        # Step 1: Create template
        self.create_environment_template()
        
        # Step 2: Validate existing environment
        validation_success = self.validate_environment_variables()
        
        # Step 3: Test PostgreSQL connection
        connection_success = self.test_postgresql_connection()
        
        # Step 4: Generate instructions
        instructions_file = self.generate_setup_instructions()
        
        # Summary
        self.print_header("SETUP SUMMARY")
        
        print(f"✅ Environment template: {self.env_template}")
        print(f"{'✅' if validation_success else '❌'} Environment validation: {'Passed' if validation_success else 'Failed'}")
        print(f"{'✅' if connection_success else '❌'} PostgreSQL connection: {'Success' if connection_success else 'Failed'}")
        print(f"✅ Setup instructions: {instructions_file}")
        
        if validation_success and connection_success:
            print("\n🎉 Database environment setup completed successfully!")
            print("The application should now use PostgreSQL instead of SQLite.")
        else:
            print("\n⚠️ Setup completed with issues.")
            print("Please follow the manual setup instructions in POSTGRESQL_SETUP.md")
        
        return validation_success and connection_success


def main():
    """Main function"""
    manager = DatabaseEnvironmentManager()
    success = manager.run_full_setup()
    
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
