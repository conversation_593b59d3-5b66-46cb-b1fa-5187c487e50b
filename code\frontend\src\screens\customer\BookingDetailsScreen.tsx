/**
 * Booking Details Screen
 * Customer-specific screen for viewing booking details
 */

import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

interface BookingDetailsScreenProps {
  route?: {
    params?: {
      bookingId?: string;
    };
  };
}

export const BookingDetailsScreen: React.FC<BookingDetailsScreenProps> = ({ route }) => {
  const bookingId = route?.params?.bookingId;

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content}>
        <Text style={styles.title}>Booking Details</Text>
        <Text style={styles.subtitle}>Booking ID: {bookingId || 'N/A'}</Text>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Booking Information</Text>
          <Text style={styles.text}>
            This is a placeholder for the booking details screen. 
            This screen will display detailed information about a specific booking.
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Features</Text>
          <Text style={styles.text}>• Booking status and timeline</Text>
          <Text style={styles.text}>• Service provider information</Text>
          <Text style={styles.text}>• Appointment date and time</Text>
          <Text style={styles.text}>• Service details and pricing</Text>
          <Text style={styles.text}>• Cancellation and rescheduling</Text>
          <Text style={styles.text}>• Communication with provider</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    marginBottom: 24,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 12,
  },
  text: {
    fontSize: 16,
    color: '#333333',
    lineHeight: 24,
    marginBottom: 8,
  },
});

export default BookingDetailsScreen;
