#!/usr/bin/env python3
"""
JWT RS256 Implementation Test

This script tests the JWT RS256 implementation with RSA keys.
Part of EPIC-AUDIT-002 - Production Security Implementation.

Tests:
- RSA key loading from environment variables
- JWT token generation with RS256 algorithm
- JWT token verification with public key
- Token payload validation
- Error handling and fallback mechanisms
"""

import os
import sys
import django
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'vierla_project.settings.development')
django.setup()

import json
from datetime import datetime, timedelta
from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import RefreshToken, AccessToken
from rest_framework_simplejwt.exceptions import TokenError, InvalidToken
from vierla_project.utils.jwt_keys import validate_jwt_configuration, jwt_key_manager

User = get_user_model()

def test_jwt_key_configuration():
    """Test JWT key configuration and loading."""
    print("🔐 Testing JWT Key Configuration")
    print("=" * 50)
    
    # Test key validation
    validation_result = validate_jwt_configuration()
    
    if validation_result['status'] == 'success':
        print("✅ JWT key configuration validation successful")
        print(f"   Key Info: {validation_result['key_info']}")
        print(f"   Private Key Length: {validation_result['private_key_length']} characters")
        print(f"   Public Key Length: {validation_result['public_key_length']} characters")
        return True
    else:
        print(f"❌ JWT key configuration validation failed: {validation_result['message']}")
        return False

def test_jwt_token_generation():
    """Test JWT token generation with RS256."""
    print("\n🎫 Testing JWT Token Generation")
    print("=" * 50)
    
    try:
        # Create or get a test user
        user, created = User.objects.get_or_create(
            username='jwt_test_user',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'JWT',
                'last_name': 'Test',
                'is_verified': True
            }
        )
        
        if created:
            user.set_password('testpassword123')
            user.save()
            print(f"✅ Created test user: {user.username}")
        else:
            print(f"✅ Using existing test user: {user.username}")
        
        # Generate refresh token
        refresh = RefreshToken.for_user(user)
        access = refresh.access_token
        
        print(f"✅ Generated refresh token (length: {len(str(refresh))} characters)")
        print(f"✅ Generated access token (length: {len(str(access))} characters)")
        
        # Display token payload
        print(f"\n📋 Access Token Payload:")
        for key, value in access.payload.items():
            if key == 'exp':
                exp_time = datetime.fromtimestamp(value)
                print(f"   {key}: {value} ({exp_time})")
            elif key == 'iat':
                iat_time = datetime.fromtimestamp(value)
                print(f"   {key}: {value} ({iat_time})")
            else:
                print(f"   {key}: {value}")
        
        return str(refresh), str(access), user
        
    except Exception as e:
        print(f"❌ JWT token generation failed: {e}")
        return None, None, None

def test_jwt_token_verification(access_token_str, refresh_token_str):
    """Test JWT token verification."""
    print("\n🔍 Testing JWT Token Verification")
    print("=" * 50)
    
    try:
        # Verify access token
        access_token = AccessToken(access_token_str)
        print("✅ Access token verification successful")
        print(f"   Token Type: {access_token.get('token_type', 'N/A')}")
        print(f"   User ID: {access_token.get('user_id', 'N/A')}")
        print(f"   JTI: {access_token.get('jti', 'N/A')}")
        
        # Verify refresh token
        refresh_token = RefreshToken(refresh_token_str)
        print("✅ Refresh token verification successful")
        print(f"   Token Type: {refresh_token.get('token_type', 'N/A')}")
        print(f"   User ID: {refresh_token.get('user_id', 'N/A')}")
        
        return True
        
    except TokenError as e:
        print(f"❌ Token verification failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error during token verification: {e}")
        return False

def test_jwt_token_refresh(refresh_token_str):
    """Test JWT token refresh functionality."""
    print("\n🔄 Testing JWT Token Refresh")
    print("=" * 50)
    
    try:
        # Create refresh token object
        refresh_token = RefreshToken(refresh_token_str)
        
        # Generate new access token
        new_access_token = refresh_token.access_token
        
        print("✅ Token refresh successful")
        print(f"   New access token length: {len(str(new_access_token))} characters")
        print(f"   New token expires at: {datetime.fromtimestamp(new_access_token['exp'])}")
        
        return str(new_access_token)
        
    except Exception as e:
        print(f"❌ Token refresh failed: {e}")
        return None

def test_jwt_algorithm_verification():
    """Test that JWT is using the correct algorithm."""
    print("\n🔧 Testing JWT Algorithm Configuration")
    print("=" * 50)
    
    try:
        from django.conf import settings
        
        jwt_config = settings.SIMPLE_JWT
        algorithm = jwt_config.get('ALGORITHM')
        signing_key = jwt_config.get('SIGNING_KEY')
        verifying_key = jwt_config.get('VERIFYING_KEY')
        
        print(f"✅ JWT Algorithm: {algorithm}")
        
        if algorithm == 'RS256':
            print("✅ Using RS256 (asymmetric) algorithm - SECURE")
            print(f"   Signing key type: {'RSA Private Key' if '-----BEGIN PRIVATE KEY-----' in signing_key else 'Unknown'}")
            print(f"   Verifying key type: {'RSA Public Key' if verifying_key and '-----BEGIN PUBLIC KEY-----' in verifying_key else 'Unknown'}")
            return True
        elif algorithm == 'HS256':
            print("⚠️ Using HS256 (symmetric) algorithm - DEVELOPMENT ONLY")
            print("   This is acceptable for development but NOT for production")
            return False
        else:
            print(f"❌ Unknown algorithm: {algorithm}")
            return False
            
    except Exception as e:
        print(f"❌ Error checking JWT algorithm: {e}")
        return False

def test_jwt_security_features():
    """Test JWT security features."""
    print("\n🛡️ Testing JWT Security Features")
    print("=" * 50)
    
    try:
        from django.conf import settings
        jwt_config = settings.SIMPLE_JWT
        
        # Check token lifetimes
        access_lifetime = jwt_config.get('ACCESS_TOKEN_LIFETIME')
        refresh_lifetime = jwt_config.get('REFRESH_TOKEN_LIFETIME')
        
        print(f"✅ Access token lifetime: {access_lifetime}")
        print(f"✅ Refresh token lifetime: {refresh_lifetime}")
        
        # Check security features
        rotate_refresh = jwt_config.get('ROTATE_REFRESH_TOKENS')
        blacklist_after_rotation = jwt_config.get('BLACKLIST_AFTER_ROTATION')
        
        print(f"✅ Rotate refresh tokens: {rotate_refresh}")
        print(f"✅ Blacklist after rotation: {blacklist_after_rotation}")
        
        # Check issuer and leeway
        issuer = jwt_config.get('ISSUER')
        leeway = jwt_config.get('LEEWAY')
        
        print(f"✅ Token issuer: {issuer}")
        print(f"✅ Clock skew leeway: {leeway} seconds")
        
        # Security assessment
        security_score = 0
        if access_lifetime and access_lifetime <= timedelta(minutes=30):
            security_score += 1
            print("   ✅ Access token lifetime is secure (≤30 minutes)")
        else:
            print("   ⚠️ Access token lifetime could be shorter for better security")
        
        if rotate_refresh:
            security_score += 1
            print("   ✅ Refresh token rotation enabled")
        
        if blacklist_after_rotation:
            security_score += 1
            print("   ✅ Token blacklisting enabled")
        
        if issuer:
            security_score += 1
            print("   ✅ Token issuer configured")
        
        print(f"\n📊 Security Score: {security_score}/4")
        return security_score >= 3
        
    except Exception as e:
        print(f"❌ Error testing security features: {e}")
        return False

def cleanup_test_data():
    """Clean up test data."""
    try:
        User.objects.filter(username='jwt_test_user').delete()
        print("🧹 Test user cleaned up")
    except Exception as e:
        print(f"⚠️ Error cleaning up test data: {e}")

def main():
    """Main test function."""
    print("🚀 JWT RS256 Implementation Test Suite")
    print("=" * 60)
    
    test_results = []
    
    # Test 1: Key Configuration
    result1 = test_jwt_key_configuration()
    test_results.append(("Key Configuration", result1))
    
    # Test 2: Algorithm Verification
    result2 = test_jwt_algorithm_verification()
    test_results.append(("Algorithm Configuration", result2))
    
    # Test 3: Token Generation
    refresh_token, access_token, user = test_jwt_token_generation()
    result3 = refresh_token is not None and access_token is not None
    test_results.append(("Token Generation", result3))
    
    if result3:
        # Test 4: Token Verification
        result4 = test_jwt_token_verification(access_token, refresh_token)
        test_results.append(("Token Verification", result4))
        
        # Test 5: Token Refresh
        new_access_token = test_jwt_token_refresh(refresh_token)
        result5 = new_access_token is not None
        test_results.append(("Token Refresh", result5))
    else:
        test_results.append(("Token Verification", False))
        test_results.append(("Token Refresh", False))
    
    # Test 6: Security Features
    result6 = test_jwt_security_features()
    test_results.append(("Security Features", result6))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:25} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! JWT RS256 implementation is working correctly.")
    elif passed >= total * 0.8:
        print("⚠️ Most tests passed. Minor issues detected.")
    else:
        print("❌ Significant issues detected. JWT implementation needs attention.")
    
    # Cleanup
    cleanup_test_data()
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
