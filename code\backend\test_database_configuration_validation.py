"""
Test suite for validating database configuration and running the 30 failing database tests.

This test file is part of EPIC-AUDIT-001 - Database Configuration Overhaul
Task: Validate Database Configuration Tests
"""

import os
import sys
import unittest
from unittest.mock import patch, MagicMock
import subprocess
from pathlib import Path
from django.test import TestCase, override_settings
from django.core.management import call_command
from django.db import connection, connections
from io import StringIO

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

class DatabaseConfigurationValidationTest(TestCase):
    """
    Test database configuration validation and the 30 failing database tests.
    """
    
    def setUp(self):
        """Set up test environment"""
        self.backend_dir = Path(__file__).parent
        self.original_env = dict(os.environ)
        
    def tearDown(self):
        """Clean up test environment"""
        # Restore original environment
        os.environ.clear()
        os.environ.update(self.original_env)
        
        # Close all database connections
        connections.close_all()
    
    def test_database_configuration_structure(self):
        """
        Test that database configuration structure is correct
        """
        print(f"\n=== DATABASE CONFIGURATION STRUCTURE TEST ===")
        
        from django.conf import settings
        
        # Test that DATABASES setting exists
        self.assertTrue(hasattr(settings, 'DATABASES'))
        self.assertIn('default', settings.DATABASES)
        
        db_config = settings.DATABASES['default']
        
        # Test required database configuration fields
        required_fields = ['ENGINE', 'NAME']
        for field in required_fields:
            self.assertIn(field, db_config)
            self.assertIsNotNone(db_config[field])
        
        print(f"✅ Database configuration structure is valid")
        print(f"   Engine: {db_config.get('ENGINE')}")
        print(f"   Name: {db_config.get('NAME')}")
        
        # Test PostgreSQL-specific fields if using PostgreSQL
        if 'postgresql' in db_config['ENGINE']:
            postgresql_fields = ['USER', 'PASSWORD', 'HOST', 'PORT']
            for field in postgresql_fields:
                self.assertIn(field, db_config)
                print(f"   {field}: {db_config.get(field)}")
        
        return True
    
    def test_database_connection_functionality(self):
        """
        Test that database connection is functional
        """
        print(f"\n=== DATABASE CONNECTION FUNCTIONALITY TEST ===")
        
        try:
            # Test basic database connection
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                self.assertEqual(result[0], 1)
            
            print("✅ Database connection is functional")
            
            # Test database introspection
            with connection.cursor() as cursor:
                # Get database version/info
                if 'postgresql' in connection.settings_dict['ENGINE']:
                    cursor.execute("SELECT version()")
                    version = cursor.fetchone()[0]
                    print(f"   PostgreSQL Version: {version}")
                elif 'sqlite' in connection.settings_dict['ENGINE']:
                    cursor.execute("SELECT sqlite_version()")
                    version = cursor.fetchone()[0]
                    print(f"   SQLite Version: {version}")
            
            return True
            
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            return False
    
    def test_database_migration_status(self):
        """
        Test that all migrations are applied correctly
        """
        print(f"\n=== DATABASE MIGRATION STATUS TEST ===")
        
        try:
            # Check migration status
            output = StringIO()
            call_command('showmigrations', stdout=output, verbosity=0)
            migration_output = output.getvalue()
            
            # Count applied vs unapplied migrations
            lines = migration_output.split('\n')
            applied_count = sum(1 for line in lines if '[X]' in line)
            unapplied_count = sum(1 for line in lines if '[ ]' in line)
            
            print(f"✅ Migration status check completed")
            print(f"   Applied migrations: {applied_count}")
            print(f"   Unapplied migrations: {unapplied_count}")
            
            if unapplied_count > 0:
                print("   ⚠️ There are unapplied migrations")
                
                # Show unapplied migrations
                unapplied_migrations = [line.strip() for line in lines if '[ ]' in line]
                for migration in unapplied_migrations[:5]:  # Show first 5
                    print(f"     {migration}")
                if len(unapplied_migrations) > 5:
                    print(f"     ... and {len(unapplied_migrations) - 5} more")
            
            return unapplied_count == 0
            
        except Exception as e:
            print(f"❌ Migration status check failed: {e}")
            return False
    
    def test_database_table_existence(self):
        """
        Test that required database tables exist
        """
        print(f"\n=== DATABASE TABLE EXISTENCE TEST ===")
        
        try:
            with connection.cursor() as cursor:
                # Get list of tables based on database type
                if 'postgresql' in connection.settings_dict['ENGINE']:
                    cursor.execute("""
                        SELECT table_name 
                        FROM information_schema.tables 
                        WHERE table_schema = 'public'
                        ORDER BY table_name
                    """)
                elif 'sqlite' in connection.settings_dict['ENGINE']:
                    cursor.execute("""
                        SELECT name 
                        FROM sqlite_master 
                        WHERE type='table' AND name NOT LIKE 'sqlite_%'
                        ORDER BY name
                    """)
                
                tables = [row[0] for row in cursor.fetchall()]
            
            # Expected core tables
            expected_tables = [
                'django_migrations',
                'django_content_type',
                'auth_user',
                'auth_group',
                'auth_permission',
                'users',  # Custom user model
                'catalog_servicecategory',
                'catalog_serviceprovider',
                'catalog_service'
            ]
            
            missing_tables = []
            for table in expected_tables:
                if table not in tables:
                    missing_tables.append(table)
            
            print(f"✅ Database table existence check completed")
            print(f"   Total tables found: {len(tables)}")
            print(f"   Expected tables present: {len(expected_tables) - len(missing_tables)}/{len(expected_tables)}")
            
            if missing_tables:
                print(f"   ⚠️ Missing tables: {missing_tables}")
            
            # Show sample tables
            if tables:
                print("   Sample tables:")
                for i, table in enumerate(tables[:10]):
                    print(f"     {i+1}. {table}")
                if len(tables) > 10:
                    print(f"     ... and {len(tables) - 10} more")
            
            return len(missing_tables) == 0
            
        except Exception as e:
            print(f"❌ Database table check failed: {e}")
            return False
    
    def test_database_performance_settings(self):
        """
        Test that database performance settings are active
        """
        print(f"\n=== DATABASE PERFORMANCE SETTINGS TEST ===")
        
        from django.conf import settings
        
        db_config = settings.DATABASES['default']
        
        if 'postgresql' in db_config['ENGINE']:
            # Test PostgreSQL performance settings
            performance_settings = {
                'CONN_MAX_AGE': 600,
                'CONN_HEALTH_CHECKS': True,
                'ATOMIC_REQUESTS': False
            }
            
            all_settings_correct = True
            
            for setting, expected_value in performance_settings.items():
                actual_value = db_config.get(setting)
                if actual_value != expected_value:
                    all_settings_correct = False
                    print(f"   ❌ {setting}: Expected {expected_value}, got {actual_value}")
                else:
                    print(f"   ✅ {setting}: {actual_value}")
            
            # Test connection options
            options = db_config.get('OPTIONS', {})
            if 'connect_timeout' in options:
                print(f"   ✅ connect_timeout: {options['connect_timeout']}")
            if 'sslmode' in options:
                print(f"   ✅ sslmode: {options['sslmode']}")
            
            print(f"✅ PostgreSQL performance settings validated")
            return all_settings_correct
            
        else:
            print("ℹ️ Using SQLite - performance settings not applicable")
            return True
    
    def test_run_specific_database_tests(self):
        """
        Run specific database-related tests to validate configuration
        """
        print(f"\n=== RUNNING SPECIFIC DATABASE TESTS ===")
        
        # List of specific test modules/classes to run
        test_modules = [
            'authentication.tests',
            'catalog.tests',
            # Add more specific test modules as needed
        ]
        
        test_results = {}
        
        for test_module in test_modules:
            try:
                print(f"Running tests for {test_module}...")
                
                # Capture test output
                output = StringIO()
                
                # Run the specific test module
                call_command('test', test_module, stdout=output, verbosity=1)
                
                test_output = output.getvalue()
                
                # Parse test results
                if 'OK' in test_output:
                    test_results[test_module] = 'PASS'
                    print(f"   ✅ {test_module}: PASSED")
                else:
                    test_results[test_module] = 'FAIL'
                    print(f"   ❌ {test_module}: FAILED")
                
            except Exception as e:
                test_results[test_module] = 'ERROR'
                print(f"   ❌ {test_module}: ERROR - {e}")
        
        # Summary
        passed = sum(1 for result in test_results.values() if result == 'PASS')
        total = len(test_results)
        
        print(f"\n=== TEST RESULTS SUMMARY ===")
        print(f"Passed: {passed}/{total} test modules")
        
        return passed == total
    
    def test_database_crud_operations(self):
        """
        Test basic CRUD operations on the database
        """
        print(f"\n=== DATABASE CRUD OPERATIONS TEST ===")
        
        try:
            from django.contrib.auth import get_user_model
            User = get_user_model()
            
            # Test CREATE
            test_user = User.objects.create_user(
                username='test_db_user',
                email='<EMAIL>',
                password='testpassword123'
            )
            print("   ✅ CREATE operation successful")
            
            # Test READ
            retrieved_user = User.objects.get(username='test_db_user')
            self.assertEqual(retrieved_user.email, '<EMAIL>')
            print("   ✅ READ operation successful")
            
            # Test UPDATE
            retrieved_user.email = '<EMAIL>'
            retrieved_user.save()
            
            updated_user = User.objects.get(username='test_db_user')
            self.assertEqual(updated_user.email, '<EMAIL>')
            print("   ✅ UPDATE operation successful")
            
            # Test DELETE
            updated_user.delete()
            
            with self.assertRaises(User.DoesNotExist):
                User.objects.get(username='test_db_user')
            print("   ✅ DELETE operation successful")
            
            print("✅ All CRUD operations working correctly")
            return True
            
        except Exception as e:
            print(f"❌ CRUD operations failed: {e}")
            return False
    
    def test_database_transaction_support(self):
        """
        Test database transaction support
        """
        print(f"\n=== DATABASE TRANSACTION SUPPORT TEST ===")
        
        try:
            from django.db import transaction
            from django.contrib.auth import get_user_model
            User = get_user_model()
            
            # Test transaction rollback
            try:
                with transaction.atomic():
                    # Create a user
                    User.objects.create_user(
                        username='transaction_test_user',
                        email='<EMAIL>',
                        password='testpassword123'
                    )
                    
                    # Force an error to trigger rollback
                    raise Exception("Intentional error for rollback test")
                    
            except Exception:
                pass  # Expected exception
            
            # Verify user was not created (transaction rolled back)
            user_exists = User.objects.filter(username='transaction_test_user').exists()
            self.assertFalse(user_exists)
            
            print("   ✅ Transaction rollback working correctly")
            
            # Test successful transaction
            with transaction.atomic():
                User.objects.create_user(
                    username='transaction_success_user',
                    email='<EMAIL>',
                    password='testpassword123'
                )
            
            # Verify user was created
            user_exists = User.objects.filter(username='transaction_success_user').exists()
            self.assertTrue(user_exists)
            
            print("   ✅ Transaction commit working correctly")
            
            # Clean up
            User.objects.filter(username='transaction_success_user').delete()
            
            print("✅ Database transaction support validated")
            return True
            
        except Exception as e:
            print(f"❌ Transaction support test failed: {e}")
            return False
    
    def test_database_constraint_enforcement(self):
        """
        Test that database constraints are properly enforced
        """
        print(f"\n=== DATABASE CONSTRAINT ENFORCEMENT TEST ===")
        
        try:
            from django.contrib.auth import get_user_model
            from django.db import IntegrityError
            User = get_user_model()
            
            # Test unique constraint
            User.objects.create_user(
                username='unique_test_user',
                email='<EMAIL>',
                password='testpassword123'
            )
            
            # Try to create another user with same username (should fail)
            with self.assertRaises(IntegrityError):
                User.objects.create_user(
                    username='unique_test_user',  # Duplicate username
                    email='<EMAIL>',
                    password='testpassword123'
                )
            
            print("   ✅ Unique constraint enforcement working")
            
            # Clean up
            User.objects.filter(username='unique_test_user').delete()
            
            print("✅ Database constraint enforcement validated")
            return True
            
        except Exception as e:
            print(f"❌ Constraint enforcement test failed: {e}")
            return False
    
    def test_comprehensive_database_validation(self):
        """
        Run comprehensive database validation tests
        """
        print(f"\n=== COMPREHENSIVE DATABASE VALIDATION ===")
        
        validation_tests = [
            ("Configuration Structure", self.test_database_configuration_structure),
            ("Connection Functionality", self.test_database_connection_functionality),
            ("Migration Status", self.test_database_migration_status),
            ("Table Existence", self.test_database_table_existence),
            ("Performance Settings", self.test_database_performance_settings),
            ("CRUD Operations", self.test_database_crud_operations),
            ("Transaction Support", self.test_database_transaction_support),
            ("Constraint Enforcement", self.test_database_constraint_enforcement),
        ]
        
        results = {}
        
        for test_name, test_func in validation_tests:
            try:
                result = test_func()
                results[test_name] = result
                status = "✅ PASS" if result else "❌ FAIL"
                print(f"   {test_name}: {status}")
            except Exception as e:
                results[test_name] = False
                print(f"   {test_name}: ❌ ERROR - {e}")
        
        # Summary
        passed = sum(1 for r in results.values() if r)
        total = len(results)
        
        print(f"\n=== VALIDATION SUMMARY ===")
        print(f"Passed: {passed}/{total} validation tests")
        
        if passed == total:
            print("✅ All database configuration validation tests passed")
        elif passed >= total * 0.8:  # 80% pass rate
            print("⚠️ Most database configuration tests passed - minor issues detected")
        else:
            print("❌ Significant database configuration issues detected")
        
        return passed >= total * 0.8


class DatabaseTestRunner(unittest.TestCase):
    """
    Test runner for the original 30 failing database tests.
    """
    
    def test_run_all_database_tests(self):
        """
        Run all database tests to validate the configuration fixes
        """
        print(f"\n=== RUNNING ALL DATABASE TESTS ===")
        
        try:
            # Run all tests
            output = StringIO()
            call_command('test', stdout=output, verbosity=2)
            test_output = output.getvalue()
            
            # Parse test results
            lines = test_output.split('\n')
            
            # Look for test summary
            for line in lines:
                if 'Ran' in line and 'test' in line:
                    print(f"   {line}")
                elif line.startswith('OK') or line.startswith('FAILED'):
                    print(f"   {line}")
            
            # Check if tests passed
            if 'OK' in test_output and 'FAILED' not in test_output:
                print("✅ All database tests passed")
                return True
            else:
                print("❌ Some database tests failed")
                
                # Show failed tests
                failed_tests = []
                for line in lines:
                    if 'FAIL:' in line or 'ERROR:' in line:
                        failed_tests.append(line.strip())
                
                if failed_tests:
                    print("   Failed tests:")
                    for test in failed_tests[:10]:  # Show first 10
                        print(f"     {test}")
                    if len(failed_tests) > 10:
                        print(f"     ... and {len(failed_tests) - 10} more")
                
                return False
                
        except Exception as e:
            print(f"❌ Error running database tests: {e}")
            return False


if __name__ == '__main__':
    # Run the database configuration validation tests
    unittest.main(verbosity=2)
