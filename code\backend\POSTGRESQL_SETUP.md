
# PostgreSQL Setup Instructions for Vierla Application

## Prerequisites
1. PostgreSQL must be installed and running
2. You need superuser access to PostgreSQL

## Automatic Setup (Recommended)
Run the database setup script:
```bash
python setup_database_configuration.py
```

## Manual Setup
If automatic setup fails, follow these steps:

### 1. Connect to PostgreSQL as superuser
```bash
psql -U postgres
```

### 2. Create database and user
```sql
-- Create user
CREATE USER vierla_user WITH PASSWORD 'vierla_password';

-- Create database
CREATE DATABASE vierla_db OWNER vierla_user;

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE vierla_db TO vierla_user;

-- Exit PostgreSQL
\q
```

### 3. Test connection
```bash
psql -U vierla_user -d vierla_db -h localhost
```

### 4. Update environment variables
Edit .env file if needed:
```
DB_NAME=vierla_db
DB_USER=vierla_user
DB_PASSWORD=vierla_password
DB_HOST=localhost
DB_PORT=5432
USE_SQLITE=false
```

### 5. Run Django migrations
```bash
python manage.py migrate
```

## Troubleshooting

### PostgreSQL Authentication Issues
If you get authentication errors:

1. Check PostgreSQL is running:
   ```bash
   # Windows
   net start postgresql-x64-17
   
   # Linux/Mac
   sudo systemctl status postgresql
   ```

2. Reset postgres user password:
   ```bash
   # Windows (as Administrator)
   psql -U postgres
   ALTER USER postgres PASSWORD 'postgres';
   
   # Linux/Mac
   sudo -u postgres psql
   ALTER USER postgres PASSWORD 'postgres';
   ```

3. Check pg_hba.conf for authentication method
   - Location: PostgreSQL data directory
   - Change 'md5' to 'trust' for local connections (development only)

### Connection Refused
- Verify PostgreSQL is running on correct port (5432)
- Check firewall settings
- Verify PostgreSQL accepts connections on localhost

### Database Does Not Exist
- Run the CREATE DATABASE command manually
- Verify user has correct permissions

## Environment Variables Reference

| Variable | Description | Default |
|----------|-------------|---------|
| DB_NAME | Database name | vierla_db |
| DB_USER | Database user | vierla_user |
| DB_PASSWORD | Database password | vierla_password |
| DB_HOST | Database host | localhost |
| DB_PORT | Database port | 5432 |
| DB_SSLMODE | SSL mode | prefer |
| USE_SQLITE | Use SQLite fallback | false |

## Security Notes
- Change default passwords in production
- Use environment-specific .env files
- Never commit .env files to version control
- Use strong passwords for production databases
