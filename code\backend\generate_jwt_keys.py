#!/usr/bin/env python3
"""
JWT RSA Key Pair Generator for Production Security

This script generates RSA key pairs for JWT RS256 algorithm implementation.
Part of EPIC-AUDIT-002 - Production Security Implementation.

Usage:
    python generate_jwt_keys.py

Output:
    - jwt_private_key.pem: Private key for signing tokens
    - jwt_public_key.pem: Public key for verifying tokens
    - Environment variables for .env file
"""

import os
import sys
from pathlib import Path
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography.hazmat.backends import default_backend
import base64

def generate_rsa_key_pair(key_size=2048):
    """
    Generate RSA key pair for JWT signing and verification.
    
    Args:
        key_size (int): RSA key size in bits (default: 2048)
        
    Returns:
        tuple: (private_key, public_key) cryptography objects
    """
    print(f"🔐 Generating RSA key pair ({key_size} bits)...")
    
    # Generate private key
    private_key = rsa.generate_private_key(
        public_exponent=65537,
        key_size=key_size,
        backend=default_backend()
    )
    
    # Get public key from private key
    public_key = private_key.public_key()
    
    print("✅ RSA key pair generated successfully")
    return private_key, public_key

def serialize_private_key(private_key, password=None):
    """
    Serialize private key to PEM format.
    
    Args:
        private_key: RSA private key object
        password (bytes): Optional password for key encryption
        
    Returns:
        bytes: PEM-encoded private key
    """
    encryption_algorithm = serialization.NoEncryption()
    if password:
        encryption_algorithm = serialization.BestAvailableEncryption(password)
    
    pem_private = private_key.private_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PrivateFormat.PKCS8,
        encryption_algorithm=encryption_algorithm
    )
    
    return pem_private

def serialize_public_key(public_key):
    """
    Serialize public key to PEM format.
    
    Args:
        public_key: RSA public key object
        
    Returns:
        bytes: PEM-encoded public key
    """
    pem_public = public_key.public_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PublicFormat.SubjectPublicKeyInfo
    )
    
    return pem_public

def save_keys_to_files(private_key_pem, public_key_pem, output_dir="."):
    """
    Save key pair to PEM files.
    
    Args:
        private_key_pem (bytes): PEM-encoded private key
        public_key_pem (bytes): PEM-encoded public key
        output_dir (str): Directory to save keys (default: current directory)
    """
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # Save private key
    private_key_file = output_path / "jwt_private_key.pem"
    with open(private_key_file, 'wb') as f:
        f.write(private_key_pem)
    
    # Set restrictive permissions on private key (Unix-like systems)
    if os.name != 'nt':  # Not Windows
        os.chmod(private_key_file, 0o600)
    
    # Save public key
    public_key_file = output_path / "jwt_public_key.pem"
    with open(public_key_file, 'wb') as f:
        f.write(public_key_pem)
    
    print(f"🔑 Private key saved to: {private_key_file}")
    print(f"🔓 Public key saved to: {public_key_file}")
    
    return private_key_file, public_key_file

def generate_env_variables(private_key_pem, public_key_pem):
    """
    Generate environment variables for .env file.
    
    Args:
        private_key_pem (bytes): PEM-encoded private key
        public_key_pem (bytes): PEM-encoded public key
        
    Returns:
        str: Environment variables formatted for .env file
    """
    # Convert to base64 for environment variables (single line)
    private_key_b64 = base64.b64encode(private_key_pem).decode('utf-8')
    public_key_b64 = base64.b64encode(public_key_pem).decode('utf-8')
    
    env_content = f"""
# JWT RSA Keys for RS256 Algorithm
# Generated on: {os.popen('date').read().strip() if os.name != 'nt' else 'Windows'}

# JWT Private Key (Base64 encoded)
JWT_PRIVATE_KEY_B64={private_key_b64}

# JWT Public Key (Base64 encoded)
JWT_PUBLIC_KEY_B64={public_key_b64}

# Alternative: Direct PEM format (multiline)
JWT_PRIVATE_KEY="{private_key_pem.decode('utf-8').replace(chr(10), '\\n')}"
JWT_PUBLIC_KEY="{public_key_pem.decode('utf-8').replace(chr(10), '\\n')}"
"""
    
    return env_content.strip()

def validate_key_pair(private_key, public_key):
    """
    Validate that the key pair works correctly for JWT signing.
    
    Args:
        private_key: RSA private key object
        public_key: RSA public key object
        
    Returns:
        bool: True if validation successful
    """
    try:
        # Test signing and verification
        from cryptography.hazmat.primitives import hashes
        from cryptography.hazmat.primitives.asymmetric import padding
        
        test_message = b"JWT validation test message"
        
        # Sign with private key
        signature = private_key.sign(
            test_message,
            padding.PSS(
                mgf=padding.MGF1(hashes.SHA256()),
                salt_length=padding.PSS.MAX_LENGTH
            ),
            hashes.SHA256()
        )
        
        # Verify with public key
        public_key.verify(
            signature,
            test_message,
            padding.PSS(
                mgf=padding.MGF1(hashes.SHA256()),
                salt_length=padding.PSS.MAX_LENGTH
            ),
            hashes.SHA256()
        )
        
        print("✅ Key pair validation successful")
        return True
        
    except Exception as e:
        print(f"❌ Key pair validation failed: {e}")
        return False

def main():
    """Main function to generate JWT RSA key pair."""
    print("🚀 JWT RSA Key Pair Generator")
    print("=" * 50)
    
    try:
        # Generate key pair
        private_key, public_key = generate_rsa_key_pair(2048)
        
        # Validate key pair
        if not validate_key_pair(private_key, public_key):
            print("❌ Key pair validation failed. Exiting.")
            sys.exit(1)
        
        # Serialize keys
        private_key_pem = serialize_private_key(private_key)
        public_key_pem = serialize_public_key(public_key)
        
        # Save to files
        private_file, public_file = save_keys_to_files(
            private_key_pem, 
            public_key_pem,
            output_dir="."
        )
        
        # Generate environment variables
        env_vars = generate_env_variables(private_key_pem, public_key_pem)
        
        # Save environment variables to file
        env_file = Path(".") / "jwt_keys.env"
        with open(env_file, 'w') as f:
            f.write(env_vars)
        
        print(f"📝 Environment variables saved to: {env_file}")
        
        print("\n" + "=" * 50)
        print("✅ JWT RSA Key Pair Generation Complete!")
        print("\n📋 Next Steps:")
        print("1. Add the environment variables to your .env file")
        print("2. Update Django settings to use RS256 algorithm")
        print("3. Test JWT token generation and verification")
        print("4. Deploy keys securely to production environment")
        
        print("\n⚠️  Security Notes:")
        print("- Keep the private key secure and never commit to version control")
        print("- Use environment variables or secure key management in production")
        print("- Rotate keys periodically for enhanced security")
        print("- Set restrictive file permissions on key files")
        
        return True
        
    except ImportError as e:
        print(f"❌ Missing required dependency: {e}")
        print("💡 Install cryptography: pip install cryptography")
        return False
        
    except Exception as e:
        print(f"❌ Error generating keys: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
