"""
Shared API v1 URLs
Endpoints accessible to both customers and providers
"""

from django.urls import path, include
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework import status

@api_view(['GET'])
@permission_classes([AllowAny])
def shared_categories(request):
    """Shared categories endpoint"""
    # Redirect to existing categories endpoint for now
    try:
        from catalog.views import CategoryListView
        view = CategoryListView()
        view.request = request
        return view.get(request)
    except ImportError:
        return Response({
            'message': 'Categories endpoint',
            'categories': [],  # TODO: Implement categories logic
            'count': 0
        })

@api_view(['GET'])
@permission_classes([AllowAny])
def shared_locations(request):
    """Shared locations endpoint"""
    return Response({
        'message': 'Locations endpoint',
        'locations': [],  # TODO: Implement locations logic
        'count': 0
    })

@api_view(['GET'])
@permission_classes([AllowAny])
def shared_health(request):
    """Health check endpoint"""
    return Response({
        'status': 'healthy',
        'version': '1.0',
        'timestamp': '2025-08-08T02:34:00Z',
        'services': {
            'database': 'connected',
            'cache': 'available',
            'api': 'operational'
        }
    })

@api_view(['GET'])
@permission_classes([AllowAny])
def shared_root(request):
    """Shared API root endpoint"""
    return Response({
        'message': 'Shared API v1',
        'endpoints': {
            'categories': '/api/v1/shared/categories/',
            'locations': '/api/v1/shared/locations/',
            'health': '/api/v1/shared/health/',
        }
    })

urlpatterns = [
    # Shared root
    path('', shared_root, name='shared_root'),

    # Shared endpoints accessible to both roles
    path('categories/', shared_categories, name='shared_categories'),
    path('locations/', shared_locations, name='shared_locations'),
    path('health/', shared_health, name='shared_health'),
]
