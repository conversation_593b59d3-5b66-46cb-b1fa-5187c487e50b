#!/usr/bin/env python
"""
Quick test script for API v1 structure
Tests the new versioned, role-based API endpoints
"""

import requests
import json

API_BASE_URL = 'http://************:8000/api'

def test_api_v1_structure():
    """Test the new API v1 structure"""
    print('🧪 Testing API v1 Structure')
    print('=' * 50)
    
    # Test 1: API v1 root endpoint
    print('\n1. Testing API v1 root endpoint...')
    try:
        response = requests.get(f'{API_BASE_URL}/v1/')
        print(f'Status: {response.status_code}')
        if response.status_code == 200:
            print('✅ API v1 root endpoint accessible')
            print(f'Response: {json.dumps(response.json(), indent=2)}')
        else:
            print(f'⚠️  API v1 root returned status {response.status_code}')
            print(f'Response: {response.text}')
    except Exception as e:
        print(f'❌ Error accessing API v1 root: {e}')
    
    # Test 2: Customer endpoints structure
    print('\n2. Testing customer endpoints structure...')
    customer_endpoints = [
        '/v1/customer/',
        '/v1/customer/dashboard/',
        '/v1/customer/profile/',
        '/v1/customer/bookings/',
        '/v1/customer/search/',
    ]
    
    for endpoint in customer_endpoints:
        try:
            response = requests.get(f'{API_BASE_URL}{endpoint}')
            print(f'   {endpoint}: Status {response.status_code}')
            if response.status_code == 404:
                print(f'     ❌ Endpoint not found')
            elif response.status_code in [401, 403]:
                print(f'     ✅ Endpoint exists (requires auth)')
            elif response.status_code == 200:
                print(f'     ✅ Endpoint accessible')
            else:
                print(f'     ⚠️  Unexpected status')
        except Exception as e:
            print(f'     ❌ Error: {e}')
    
    # Test 3: Provider endpoints structure
    print('\n3. Testing provider endpoints structure...')
    provider_endpoints = [
        '/v1/provider/',
        '/v1/provider/dashboard/',
        '/v1/provider/profile/',
        '/v1/provider/services/',
        '/v1/provider/bookings/',
    ]
    
    for endpoint in provider_endpoints:
        try:
            response = requests.get(f'{API_BASE_URL}{endpoint}')
            print(f'   {endpoint}: Status {response.status_code}')
            if response.status_code == 404:
                print(f'     ❌ Endpoint not found')
            elif response.status_code in [401, 403]:
                print(f'     ✅ Endpoint exists (requires auth)')
            elif response.status_code == 200:
                print(f'     ✅ Endpoint accessible')
            else:
                print(f'     ⚠️  Unexpected status')
        except Exception as e:
            print(f'     ❌ Error: {e}')
    
    # Test 4: Shared endpoints structure
    print('\n4. Testing shared endpoints structure...')
    shared_endpoints = [
        '/v1/shared/',
        '/v1/shared/categories/',
        '/v1/shared/locations/',
        '/v1/shared/health/',
    ]
    
    for endpoint in shared_endpoints:
        try:
            response = requests.get(f'{API_BASE_URL}{endpoint}')
            print(f'   {endpoint}: Status {response.status_code}')
            if response.status_code == 404:
                print(f'     ❌ Endpoint not found')
            elif response.status_code in [401, 403]:
                print(f'     ⚠️  Shared endpoint requires auth (unexpected)')
            elif response.status_code == 200:
                print(f'     ✅ Endpoint accessible')
            else:
                print(f'     ⚠️  Unexpected status')
        except Exception as e:
            print(f'     ❌ Error: {e}')
    
    # Test 5: Legacy endpoints still work
    print('\n5. Testing legacy endpoints still work...')
    legacy_endpoints = [
        '/auth/profile/',
        '/catalog/categories/',
        '/catalog/services/',
    ]
    
    for endpoint in legacy_endpoints:
        try:
            response = requests.get(f'{API_BASE_URL}{endpoint}')
            print(f'   {endpoint}: Status {response.status_code}')
            if response.status_code == 404:
                print(f'     ❌ Legacy endpoint not found')
            elif response.status_code in [401, 403]:
                print(f'     ✅ Legacy endpoint exists (requires auth)')
            elif response.status_code == 200:
                print(f'     ✅ Legacy endpoint accessible')
            else:
                print(f'     ⚠️  Unexpected status')
        except Exception as e:
            print(f'     ❌ Error: {e}')
    
    print('\n🎉 API v1 Structure Test Complete!')

if __name__ == '__main__':
    test_api_v1_structure()
