"""
URL configuration for authentication app
"""

from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView
from . import views
from . import enhanced_auth_views

app_name = 'authentication'

urlpatterns = [
    # Authentication endpoints
    path('login/', views.CustomTokenObtainPairView.as_view(), name='login'),
    path('register/', views.UserRegistrationView.as_view(), name='register'),
    path('logout/', views.LogoutView.as_view(), name='logout'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('social/', views.SocialAuthView.as_view(), name='social_auth'),

    # User profile endpoints
    path('profile/', views.UserProfileView.as_view(), name='profile'),
    path('profile/update/', views.UserProfileUpdateView.as_view(), name='profile_update'),
    path('profile/details/', views.UserProfileDetailView.as_view(), name='profile_details'),
    path('status/', views.auth_status, name='auth_status'),

    # Email verification
    path('verify-email/', views.EmailVerificationView.as_view(), name='verify_email'),
    path('resend-verification/', views.ResendVerificationView.as_view(), name='resend_verification'),

    # Password management
    path('change-password/', views.ChangePasswordView.as_view(), name='change_password'),
    path('password/reset/', views.PasswordResetRequestView.as_view(), name='password_reset_request'),
    path('password/reset/confirm/', views.PasswordResetConfirmView.as_view(), name='password_reset_confirm'),

    # Enhanced Authentication Endpoints (EPIC-AUDIT-002)
    path('enhanced/login/', enhanced_auth_views.enhanced_login, name='enhanced_login'),
    path('enhanced/token/refresh/', enhanced_auth_views.enhanced_token_refresh, name='enhanced_token_refresh'),
    path('enhanced/logout/', enhanced_auth_views.enhanced_logout, name='enhanced_logout'),
    path('enhanced/sessions/', enhanced_auth_views.get_active_sessions, name='get_active_sessions'),
    path('enhanced/sessions/revoke/', enhanced_auth_views.revoke_session, name='revoke_session'),
    path('enhanced/change-password/', enhanced_auth_views.change_password_with_token_rotation, name='enhanced_change_password'),
]
