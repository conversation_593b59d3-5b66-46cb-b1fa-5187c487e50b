# Generated by Django 5.2.4 on 2025-08-08 02:18

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("authentication", "0002_add_is_test_account_field"),
    ]

    operations = [
        migrations.CreateModel(
            name="UserProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "address",
                    models.CharField(
                        blank=True, max_length=255, verbose_name="address"
                    ),
                ),
                (
                    "city",
                    models.Char<PERSON>ield(blank=True, max_length=100, verbose_name="city"),
                ),
                (
                    "state",
                    models.Char<PERSON>ield(
                        blank=True, max_length=100, verbose_name="state/province"
                    ),
                ),
                (
                    "zip_code",
                    models.CharField(
                        blank=True, max_length=20, verbose_name="zip/postal code"
                    ),
                ),
                (
                    "country",
                    models.<PERSON><PERSON><PERSON><PERSON>(
                        blank=True,
                        default="Canada",
                        max_length=100,
                        verbose_name="country",
                    ),
                ),
                (
                    "latitude",
                    models.DecimalField(
                        blank=True,
                        decimal_places=7,
                        max_digits=10,
                        null=True,
                        verbose_name="latitude",
                    ),
                ),
                (
                    "longitude",
                    models.DecimalField(
                        blank=True,
                        decimal_places=7,
                        max_digits=10,
                        null=True,
                        verbose_name="longitude",
                    ),
                ),
                (
                    "business_name",
                    models.CharField(
                        blank=True, max_length=255, verbose_name="business name"
                    ),
                ),
                (
                    "business_description",
                    models.TextField(
                        blank=True, max_length=1000, verbose_name="business description"
                    ),
                ),
                (
                    "years_of_experience",
                    models.PositiveIntegerField(
                        blank=True, null=True, verbose_name="years of experience"
                    ),
                ),
                ("website", models.URLField(blank=True, verbose_name="website")),
                (
                    "instagram",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="instagram handle"
                    ),
                ),
                (
                    "facebook",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="facebook page"
                    ),
                ),
                (
                    "search_radius",
                    models.PositiveIntegerField(
                        default=25, verbose_name="search radius (miles)"
                    ),
                ),
                (
                    "auto_accept_bookings",
                    models.BooleanField(
                        default=False, verbose_name="auto accept bookings"
                    ),
                ),
                (
                    "show_phone_publicly",
                    models.BooleanField(
                        default=False, verbose_name="show phone publicly"
                    ),
                ),
                (
                    "show_email_publicly",
                    models.BooleanField(
                        default=False, verbose_name="show email publicly"
                    ),
                ),
                (
                    "allow_reviews",
                    models.BooleanField(default=True, verbose_name="allow reviews"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="profile",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "User Profile",
                "verbose_name_plural": "User Profiles",
                "db_table": "user_profiles",
                "indexes": [
                    models.Index(
                        fields=["city", "state"], name="user_profil_city_c8a47f_idx"
                    ),
                    models.Index(
                        fields=["latitude", "longitude"],
                        name="user_profil_latitud_505ce2_idx",
                    ),
                ],
            },
        ),
    ]
