"""
Enhanced Authentication Views with Advanced Token Management

This module provides enhanced authentication views with comprehensive
token rotation and blacklisting functionality.

Part of EPIC-AUDIT-002 - Production Security Implementation.

Features:
- Enhanced login with device tracking
- Secure token refresh with rotation
- Comprehensive logout with token blacklisting
- Session management and monitoring
- Security event logging
"""

import logging
from typing import Dict, Optional
from django.contrib.auth import authenticate
from django.utils import timezone
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework.request import Request
from rest_framework_simplejwt.exceptions import TokenError, InvalidToken
from rest_framework_simplejwt.tokens import RefreshToken
from vierla_project.utils.token_management import (
    enhanced_token_manager, 
    TokenSecurityReason,
    TokenFamilyManager
)
from .serializers import UserSerializer

logger = logging.getLogger(__name__)

def get_device_info(request: Request) -> Dict:
    """
    Extract device information from request for security tracking.
    
    Args:
        request: Django request object
        
    Returns:
        Dict: Device information
    """
    return {
        'user_agent': request.META.get('HTTP_USER_AGENT', ''),
        'ip_address': request.META.get('REMOTE_ADDR', ''),
        'device_id': request.data.get('device_id', ''),
        'device_type': request.data.get('device_type', 'unknown'),
        'app_version': request.data.get('app_version', ''),
        'platform': request.data.get('platform', ''),
        'timestamp': timezone.now().isoformat()
    }

@api_view(['POST'])
@permission_classes([AllowAny])
def enhanced_login(request):
    """
    Enhanced login endpoint with comprehensive token management.
    
    Features:
    - Device fingerprinting and tracking
    - Token family creation
    - Security event logging
    - Enhanced token claims
    """
    try:
        # Extract credentials
        username = request.data.get('username')
        password = request.data.get('password')
        
        if not username or not password:
            return Response({
                'error': 'Username and password are required',
                'code': 'MISSING_CREDENTIALS'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Authenticate user (try both username and email)
        user = authenticate(username=username, password=password)
        if not user:
            # Try authenticating with email
            try:
                from django.contrib.auth import get_user_model
                User = get_user_model()
                user_obj = User.objects.get(email=username)
                user = authenticate(username=user_obj.username, password=password)
            except User.DoesNotExist:
                pass
        
        if not user:
            logger.warning(f"Failed login attempt for username: {username}")
            return Response({
                'error': 'Invalid credentials',
                'code': 'INVALID_CREDENTIALS'
            }, status=status.HTTP_401_UNAUTHORIZED)
        
        if not user.is_active:
            return Response({
                'error': 'Account is disabled',
                'code': 'ACCOUNT_DISABLED'
            }, status=status.HTTP_401_UNAUTHORIZED)
        
        # Get device information
        device_info = get_device_info(request)
        
        # Create enhanced token pair
        refresh_token, access_token = enhanced_token_manager.create_token_pair(
            user=user,
            device_info=device_info
        )
        
        # Update user last login
        user.last_login = timezone.now()
        user.last_login_ip = device_info['ip_address']
        user.save(update_fields=['last_login', 'last_login_ip'])
        
        # Prepare response
        user_data = UserSerializer(user, context={'request': request}).data
        
        response_data = {
            'access_token': str(access_token),
            'refresh_token': str(refresh_token),
            'token_type': 'Bearer',
            'expires_in': access_token.lifetime.total_seconds(),
            'user': user_data,
            'session_info': {
                'family_id': refresh_token['family_id'],
                'device_info': device_info,
                'login_time': timezone.now().isoformat()
            }
        }
        
        logger.info(f"Successful login for user {user.id} from {device_info['ip_address']}")
        
        return Response(response_data, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Login error: {e}")
        return Response({
            'error': 'Login failed',
            'code': 'LOGIN_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([AllowAny])
def enhanced_token_refresh(request):
    """
    Enhanced token refresh endpoint with comprehensive security checks.
    
    Features:
    - Token rotation with family tracking
    - Suspicious activity detection
    - Device consistency checks
    - Security event logging
    """
    try:
        refresh_token_str = request.data.get('refresh_token')
        
        if not refresh_token_str:
            return Response({
                'error': 'Refresh token is required',
                'code': 'MISSING_REFRESH_TOKEN'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Get device information
        device_info = get_device_info(request)
        
        # Rotate token with security checks
        new_refresh, new_access = enhanced_token_manager.rotate_token(
            refresh_token_str=refresh_token_str,
            device_info=device_info
        )
        
        # Prepare response
        response_data = {
            'access_token': str(new_access),
            'refresh_token': str(new_refresh),
            'token_type': 'Bearer',
            'expires_in': new_access.lifetime.total_seconds(),
            'session_info': {
                'family_id': new_refresh['family_id'],
                'refreshed_at': timezone.now().isoformat()
            }
        }
        
        logger.info(f"Token refreshed for user {new_refresh.user.id}")
        
        return Response(response_data, status=status.HTTP_200_OK)
        
    except TokenError as e:
        logger.warning(f"Token refresh failed: {e}")
        return Response({
            'error': 'Invalid or expired refresh token',
            'code': 'INVALID_REFRESH_TOKEN'
        }, status=status.HTTP_401_UNAUTHORIZED)
    except Exception as e:
        logger.error(f"Token refresh error: {e}")
        return Response({
            'error': 'Token refresh failed',
            'code': 'REFRESH_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def enhanced_logout(request):
    """
    Enhanced logout endpoint with comprehensive token blacklisting.
    
    Features:
    - Token blacklisting with reason tracking
    - Optional logout from all devices
    - Security event logging
    """
    try:
        refresh_token_str = request.data.get('refresh_token')
        logout_all_devices = request.data.get('logout_all_devices', False)
        
        user = request.user
        
        if logout_all_devices:
            # Blacklist all user tokens
            blacklisted_count = enhanced_token_manager.blacklist_all_user_tokens(
                user_id=user.id,
                reason=TokenSecurityReason.LOGOUT
            )
            
            logger.info(f"User {user.id} logged out from all devices ({blacklisted_count} tokens)")
            
            return Response({
                'message': 'Successfully logged out from all devices',
                'tokens_revoked': blacklisted_count
            }, status=status.HTTP_200_OK)
        
        elif refresh_token_str:
            # Blacklist specific token
            try:
                refresh_token = RefreshToken(refresh_token_str)
                token_jti = refresh_token['jti']
                
                success = enhanced_token_manager.blacklist_token(
                    token_jti=token_jti,
                    reason=TokenSecurityReason.LOGOUT,
                    user_id=user.id
                )
                
                if success:
                    logger.info(f"User {user.id} logged out (token {token_jti})")
                    return Response({
                        'message': 'Successfully logged out'
                    }, status=status.HTTP_200_OK)
                else:
                    return Response({
                        'message': 'Token already blacklisted'
                    }, status=status.HTTP_200_OK)
                    
            except TokenError:
                return Response({
                    'error': 'Invalid refresh token',
                    'code': 'INVALID_REFRESH_TOKEN'
                }, status=status.HTTP_400_BAD_REQUEST)
        
        else:
            return Response({
                'error': 'Refresh token is required for logout',
                'code': 'MISSING_REFRESH_TOKEN'
            }, status=status.HTTP_400_BAD_REQUEST)
        
    except Exception as e:
        logger.error(f"Logout error: {e}")
        return Response({
            'error': 'Logout failed',
            'code': 'LOGOUT_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_active_sessions(request):
    """
    Get all active sessions for the authenticated user.
    
    Features:
    - Session information with device details
    - Token family tracking
    - Security monitoring data
    """
    try:
        user = request.user
        sessions = enhanced_token_manager.get_user_active_sessions(user.id)
        
        # Enhance session data
        enhanced_sessions = []
        for session in sessions:
            enhanced_session = {
                'session_id': session['jti'],
                'family_id': session['family_id'],
                'created_at': session['created_at'],
                'expires_at': session['expires_at'],
                'device_info': session['device_info'],
                'is_current': False  # Will be determined by client
            }
            enhanced_sessions.append(enhanced_session)
        
        return Response({
            'active_sessions': enhanced_sessions,
            'total_sessions': len(enhanced_sessions)
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Get active sessions error: {e}")
        return Response({
            'error': 'Failed to retrieve active sessions',
            'code': 'SESSIONS_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def revoke_session(request):
    """
    Revoke a specific session by token JTI.
    
    Features:
    - Selective session termination
    - Security event logging
    - Token family management
    """
    try:
        session_id = request.data.get('session_id')  # This is the JTI
        
        if not session_id:
            return Response({
                'error': 'Session ID is required',
                'code': 'MISSING_SESSION_ID'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        user = request.user
        
        # Verify the session belongs to the user
        user_sessions = enhanced_token_manager.get_user_active_sessions(user.id)
        session_exists = any(s['jti'] == session_id for s in user_sessions)
        
        if not session_exists:
            return Response({
                'error': 'Session not found or does not belong to user',
                'code': 'SESSION_NOT_FOUND'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Blacklist the token
        success = enhanced_token_manager.blacklist_token(
            token_jti=session_id,
            reason=TokenSecurityReason.REVOKED,
            user_id=user.id
        )
        
        if success:
            logger.info(f"User {user.id} revoked session {session_id}")
            return Response({
                'message': 'Session successfully revoked'
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'message': 'Session was already revoked'
            }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Revoke session error: {e}")
        return Response({
            'error': 'Failed to revoke session',
            'code': 'REVOKE_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def change_password_with_token_rotation(request):
    """
    Change password with automatic token rotation for security.
    
    Features:
    - Password validation
    - Automatic token blacklisting
    - New token generation
    - Security event logging
    """
    try:
        current_password = request.data.get('current_password')
        new_password = request.data.get('new_password')
        
        if not current_password or not new_password:
            return Response({
                'error': 'Current and new passwords are required',
                'code': 'MISSING_PASSWORDS'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        user = request.user
        
        # Verify current password
        if not user.check_password(current_password):
            return Response({
                'error': 'Current password is incorrect',
                'code': 'INVALID_CURRENT_PASSWORD'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Change password
        user.set_password(new_password)
        user.save()
        
        # Blacklist all existing tokens for security
        blacklisted_count = enhanced_token_manager.blacklist_all_user_tokens(
            user_id=user.id,
            reason=TokenSecurityReason.PASSWORD_CHANGE
        )
        
        # Create new token pair
        device_info = get_device_info(request)
        refresh_token, access_token = enhanced_token_manager.create_token_pair(
            user=user,
            device_info=device_info
        )
        
        logger.info(f"Password changed for user {user.id}, {blacklisted_count} tokens revoked")
        
        return Response({
            'message': 'Password changed successfully',
            'access_token': str(access_token),
            'refresh_token': str(refresh_token),
            'token_type': 'Bearer',
            'expires_in': access_token.lifetime.total_seconds(),
            'tokens_revoked': blacklisted_count
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Change password error: {e}")
        return Response({
            'error': 'Password change failed',
            'code': 'PASSWORD_CHANGE_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
