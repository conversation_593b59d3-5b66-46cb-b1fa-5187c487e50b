"""
Management Command for Rate Limiting and DDoS Protection

This command provides utilities for managing rate limits, IP blocking,
and monitoring the rate limiting system.

Part of EPIC-AUDIT-002 - Production Security Implementation.

Usage:
    python manage.py manage_rate_limits --help
    python manage.py manage_rate_limits --block-ip ***********00 --reason "Manual block"
    python manage.py manage_rate_limits --unblock-ip ***********00
    python manage.py manage_rate_limits --whitelist-ip ***********
    python manage.py manage_rate_limits --list-blocked
    python manage.py manage_rate_limits --cleanup-expired
    python manage.py manage_rate_limits --stats
"""

import json
import time
from datetime import datetime, timedelta
from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from vierla_project.utils.rate_limiting import (
    rate_limiter, 
    ip_manager, 
    ddos_protector
)

class Command(BaseCommand):
    help = 'Manage rate limiting and DDoS protection system'
    
    def add_arguments(self, parser):
        # IP Management
        parser.add_argument(
            '--block-ip',
            type=str,
            help='Block an IP address'
        )
        
        parser.add_argument(
            '--unblock-ip',
            type=str,
            help='Unblock an IP address'
        )
        
        parser.add_argument(
            '--whitelist-ip',
            type=str,
            help='Add IP to whitelist'
        )
        
        parser.add_argument(
            '--reason',
            type=str,
            default='Manual action',
            help='Reason for blocking/unblocking'
        )
        
        parser.add_argument(
            '--duration',
            type=int,
            default=3600,
            help='Block duration in seconds (default: 3600)'
        )
        
        # Listing and monitoring
        parser.add_argument(
            '--list-blocked',
            action='store_true',
            help='List all blocked IPs'
        )
        
        parser.add_argument(
            '--list-whitelist',
            action='store_true',
            help='List all whitelisted IPs'
        )
        
        parser.add_argument(
            '--list-suspicious',
            action='store_true',
            help='List suspicious IPs'
        )
        
        parser.add_argument(
            '--stats',
            action='store_true',
            help='Show rate limiting statistics'
        )
        
        # Maintenance
        parser.add_argument(
            '--cleanup-expired',
            action='store_true',
            help='Clean up expired rate limit entries'
        )
        
        parser.add_argument(
            '--reset-limits',
            type=str,
            help='Reset rate limits for specific IP or all'
        )
        
        parser.add_argument(
            '--test-ddos',
            type=str,
            help='Test DDoS protection for IP'
        )
        
        # Configuration
        parser.add_argument(
            '--set-limit',
            type=str,
            help='Set custom rate limit (format: key:limit:window)'
        )
        
        parser.add_argument(
            '--export-config',
            type=str,
            help='Export current configuration to file'
        )
        
        parser.add_argument(
            '--import-config',
            type=str,
            help='Import configuration from file'
        )
    
    def handle(self, *args, **options):
        """Handle the management command."""
        try:
            # IP Management Commands
            if options['block_ip']:
                self.block_ip(options['block_ip'], options['reason'], options['duration'])
            
            elif options['unblock_ip']:
                self.unblock_ip(options['unblock_ip'])
            
            elif options['whitelist_ip']:
                self.whitelist_ip(options['whitelist_ip'])
            
            # Listing Commands
            elif options['list_blocked']:
                self.list_blocked_ips()
            
            elif options['list_whitelist']:
                self.list_whitelisted_ips()
            
            elif options['list_suspicious']:
                self.list_suspicious_ips()
            
            elif options['stats']:
                self.show_statistics()
            
            # Maintenance Commands
            elif options['cleanup_expired']:
                self.cleanup_expired()
            
            elif options['reset_limits']:
                self.reset_limits(options['reset_limits'])
            
            elif options['test_ddos']:
                self.test_ddos_protection(options['test_ddos'])
            
            # Configuration Commands
            elif options['set_limit']:
                self.set_custom_limit(options['set_limit'])
            
            elif options['export_config']:
                self.export_configuration(options['export_config'])
            
            elif options['import_config']:
                self.import_configuration(options['import_config'])
            
            else:
                self.print_help()
        
        except Exception as e:
            raise CommandError(f'Error executing command: {e}')
    
    def block_ip(self, ip: str, reason: str, duration: int):
        """Block an IP address."""
        try:
            ip_manager.block_ip(ip, reason, duration)
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully blocked IP {ip} for {duration} seconds. Reason: {reason}'
                )
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to block IP {ip}: {e}')
            )
    
    def unblock_ip(self, ip: str):
        """Unblock an IP address."""
        try:
            ip_manager.unblock_ip(ip)
            self.stdout.write(
                self.style.SUCCESS(f'Successfully unblocked IP {ip}')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to unblock IP {ip}: {e}')
            )
    
    def whitelist_ip(self, ip: str):
        """Add IP to whitelist."""
        try:
            ip_manager.add_to_whitelist(ip)
            self.stdout.write(
                self.style.SUCCESS(f'Successfully whitelisted IP {ip}')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to whitelist IP {ip}: {e}')
            )
    
    def list_blocked_ips(self):
        """List all blocked IPs."""
        try:
            self.stdout.write(self.style.HTTP_INFO('Blocked IPs:'))
            self.stdout.write('-' * 50)
            
            if ip_manager.redis_client:
                blocked_ips = ip_manager.redis_client.smembers(ip_manager.blocked_ips_key)
                
                if not blocked_ips:
                    self.stdout.write('No blocked IPs found.')
                    return
                
                for ip in blocked_ips:
                    # Get block info
                    block_key = f"security:block_info:{ip}"
                    block_info = ip_manager.redis_client.get(block_key)
                    
                    if block_info:
                        info = json.loads(block_info)
                        blocked_at = datetime.fromtimestamp(info['blocked_at'])
                        expires_at = datetime.fromtimestamp(info['expires_at'])
                        
                        self.stdout.write(
                            f"IP: {ip}\n"
                            f"  Reason: {info['reason']}\n"
                            f"  Blocked at: {blocked_at}\n"
                            f"  Expires at: {expires_at}\n"
                        )
                    else:
                        self.stdout.write(f"IP: {ip} (no additional info)")
            else:
                self.stdout.write('Redis not available - using cache fallback')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to list blocked IPs: {e}')
            )
    
    def list_whitelisted_ips(self):
        """List all whitelisted IPs."""
        try:
            self.stdout.write(self.style.HTTP_INFO('Whitelisted IPs:'))
            self.stdout.write('-' * 50)
            
            if ip_manager.redis_client:
                whitelisted_ips = ip_manager.redis_client.smembers(ip_manager.whitelist_key)
                
                if not whitelisted_ips:
                    self.stdout.write('No whitelisted IPs found.')
                else:
                    for ip in whitelisted_ips:
                        self.stdout.write(f"IP: {ip}")
            else:
                self.stdout.write('Redis not available - using cache fallback')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to list whitelisted IPs: {e}')
            )
    
    def list_suspicious_ips(self):
        """List suspicious IPs."""
        try:
            self.stdout.write(self.style.WARNING('Suspicious IPs:'))
            self.stdout.write('-' * 50)
            
            if ip_manager.redis_client:
                # Get all suspicious IP keys
                pattern = "security:suspicious:*"
                keys = ip_manager.redis_client.keys(pattern)
                
                if not keys:
                    self.stdout.write('No suspicious IPs found.')
                    return
                
                for key in keys:
                    ip = key.split(':')[-1]
                    suspicious_info = ip_manager.redis_client.get(key)
                    
                    if suspicious_info:
                        info = json.loads(suspicious_info)
                        marked_at = datetime.fromtimestamp(info['marked_at'])
                        
                        self.stdout.write(
                            f"IP: {ip}\n"
                            f"  Reason: {info['reason']}\n"
                            f"  Count: {info['count']}\n"
                            f"  Marked at: {marked_at}\n"
                        )
            else:
                self.stdout.write('Redis not available - using cache fallback')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to list suspicious IPs: {e}')
            )
    
    def show_statistics(self):
        """Show rate limiting statistics."""
        try:
            self.stdout.write(self.style.HTTP_INFO('Rate Limiting Statistics:'))
            self.stdout.write('=' * 50)
            
            if ip_manager.redis_client:
                # Count blocked IPs
                blocked_count = ip_manager.redis_client.scard(ip_manager.blocked_ips_key)
                
                # Count whitelisted IPs
                whitelist_count = ip_manager.redis_client.scard(ip_manager.whitelist_key)
                
                # Count suspicious IPs
                suspicious_keys = ip_manager.redis_client.keys("security:suspicious:*")
                suspicious_count = len(suspicious_keys)
                
                # Count active rate limit entries
                rate_limit_keys = ip_manager.redis_client.keys("*rate*")
                active_limits = len(rate_limit_keys)
                
                self.stdout.write(f"Blocked IPs: {blocked_count}")
                self.stdout.write(f"Whitelisted IPs: {whitelist_count}")
                self.stdout.write(f"Suspicious IPs: {suspicious_count}")
                self.stdout.write(f"Active rate limit entries: {active_limits}")
                
                # Redis info
                redis_info = ip_manager.redis_client.info()
                self.stdout.write(f"Redis memory usage: {redis_info.get('used_memory_human', 'N/A')}")
                self.stdout.write(f"Redis connected clients: {redis_info.get('connected_clients', 'N/A')}")
                
            else:
                self.stdout.write('Redis not available - limited statistics')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to show statistics: {e}')
            )
    
    def cleanup_expired(self):
        """Clean up expired rate limit entries."""
        try:
            self.stdout.write('Cleaning up expired entries...')
            
            if ip_manager.redis_client:
                # Clean up expired blocks
                blocked_ips = ip_manager.redis_client.smembers(ip_manager.blocked_ips_key)
                cleaned_count = 0
                
                for ip in blocked_ips:
                    block_key = f"security:block_info:{ip}"
                    block_info = ip_manager.redis_client.get(block_key)
                    
                    if not block_info:
                        # Remove from blocked set if no info exists
                        ip_manager.redis_client.srem(ip_manager.blocked_ips_key, ip)
                        cleaned_count += 1
                    else:
                        info = json.loads(block_info)
                        if time.time() > info['expires_at']:
                            # Remove expired block
                            ip_manager.unblock_ip(ip)
                            cleaned_count += 1
                
                self.stdout.write(
                    self.style.SUCCESS(f'Cleaned up {cleaned_count} expired entries')
                )
            else:
                self.stdout.write('Redis not available - cleanup not performed')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to cleanup expired entries: {e}')
            )
    
    def reset_limits(self, target: str):
        """Reset rate limits for IP or all."""
        try:
            if target.lower() == 'all':
                if ip_manager.redis_client:
                    # Delete all rate limit keys
                    keys = ip_manager.redis_client.keys("*rate*")
                    if keys:
                        ip_manager.redis_client.delete(*keys)
                    self.stdout.write(
                        self.style.SUCCESS('Reset all rate limits')
                    )
                else:
                    self.stdout.write('Redis not available - reset not performed')
            else:
                # Reset limits for specific IP
                if ip_manager.redis_client:
                    keys = ip_manager.redis_client.keys(f"*{target}*")
                    if keys:
                        ip_manager.redis_client.delete(*keys)
                    self.stdout.write(
                        self.style.SUCCESS(f'Reset rate limits for {target}')
                    )
                else:
                    self.stdout.write('Redis not available - reset not performed')
                    
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to reset limits: {e}')
            )
    
    def test_ddos_protection(self, ip: str):
        """Test DDoS protection for an IP."""
        try:
            self.stdout.write(f'Testing DDoS protection for IP: {ip}')
            
            # Simulate request info
            request_info = {
                'user_agent': 'test-agent',
                'endpoint': '/test',
                'method': 'GET',
                'content_type': 'application/json'
            }
            
            analysis = ddos_protector.analyze_request_pattern(ip, request_info)
            
            self.stdout.write(f"Threat level: {analysis['threat_level']}")
            self.stdout.write(f"Action: {analysis['action']}")
            self.stdout.write(f"Reasons: {', '.join(analysis['reasons'])}")
            
            if analysis['block_duration'] > 0:
                self.stdout.write(f"Block duration: {analysis['block_duration']} seconds")
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to test DDoS protection: {e}')
            )
    
    def print_help(self):
        """Print help information."""
        self.stdout.write(self.style.HTTP_INFO('Rate Limiting Management Commands:'))
        self.stdout.write('')
        self.stdout.write('IP Management:')
        self.stdout.write('  --block-ip IP --reason "reason" --duration SECONDS')
        self.stdout.write('  --unblock-ip IP')
        self.stdout.write('  --whitelist-ip IP')
        self.stdout.write('')
        self.stdout.write('Monitoring:')
        self.stdout.write('  --list-blocked')
        self.stdout.write('  --list-whitelist')
        self.stdout.write('  --list-suspicious')
        self.stdout.write('  --stats')
        self.stdout.write('')
        self.stdout.write('Maintenance:')
        self.stdout.write('  --cleanup-expired')
        self.stdout.write('  --reset-limits IP|all')
        self.stdout.write('  --test-ddos IP')
        self.stdout.write('')
        self.stdout.write('Use --help for detailed information about each command.')
    
    def set_custom_limit(self, limit_config: str):
        """Set custom rate limit."""
        # Implementation for setting custom limits
        self.stdout.write('Custom limit setting not yet implemented')
    
    def export_configuration(self, filename: str):
        """Export configuration to file."""
        # Implementation for exporting configuration
        self.stdout.write('Configuration export not yet implemented')
    
    def import_configuration(self, filename: str):
        """Import configuration from file."""
        # Implementation for importing configuration
        self.stdout.write('Configuration import not yet implemented')
