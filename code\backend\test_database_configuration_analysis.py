"""
Test suite for analyzing current database configuration and identifying gaps
compared to reference architecture.

This test file is part of EPIC-AUDIT-001 - Database Configuration Overhaul
Task: Analyze Current Database Configuration
"""

import os
import sys
import unittest
from unittest.mock import patch, MagicMock
import psycopg2
from pathlib import Path
import importlib

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

class DatabaseConfigurationAnalysisTest(unittest.TestCase):
    """
    Comprehensive analysis of current database configuration structure
    and identification of gaps compared to reference architecture.
    """
    
    def setUp(self):
        """Set up test environment"""
        self.backend_dir = Path(__file__).parent
        self.env_file = self.backend_dir / '.env'
        
    def test_environment_file_exists_and_structure(self):
        """
        Test that .env file exists and has proper structure
        """
        self.assertTrue(self.env_file.exists(), 
                       "Environment file .env should exist in backend directory")
        
        # Read and analyze .env file content
        with open(self.env_file, 'r') as f:
            env_content = f.read()
        
        # Check for required database environment variables
        required_vars = [
            'DB_NAME', 'DB_USER', 'DB_PASSWORD', 'DB_HOST', 'DB_PORT'
        ]
        
        for var in required_vars:
            self.assertIn(var, env_content, 
                         f"Environment variable {var} should be defined in .env")
    
    def test_postgresql_connection_function_exists(self):
        """
        Test that test_postgresql_connection function is properly implemented
        """
        from vierla_project.settings.development import test_postgresql_connection
        
        # Function should be callable
        self.assertTrue(callable(test_postgresql_connection))
        
        # Test function behavior with mocked connection
        with patch('psycopg2.connect') as mock_connect:
            mock_conn = MagicMock()
            mock_connect.return_value = mock_conn
            
            result = test_postgresql_connection()
            self.assertTrue(result, "Function should return True when connection succeeds")
            
            # Verify connection parameters are used correctly
            mock_connect.assert_called_once()
            call_args = mock_connect.call_args[1]
            self.assertIn('host', call_args)
            self.assertIn('port', call_args)
            self.assertIn('user', call_args)
            self.assertIn('password', call_args)
            self.assertIn('database', call_args)
            self.assertIn('connect_timeout', call_args)
    
    def test_postgresql_connection_failure_handling(self):
        """
        Test that PostgreSQL connection failure is handled gracefully
        """
        from vierla_project.settings.development import test_postgresql_connection
        
        # Test with connection failure
        with patch('psycopg2.connect', side_effect=psycopg2.OperationalError("Connection failed")):
            result = test_postgresql_connection()
            self.assertFalse(result, "Function should return False when connection fails")
    
    def test_current_database_fallback_behavior(self):
        """
        Test current database fallback behavior and identify why PostgreSQL fails
        """
        # Test actual PostgreSQL connection with current environment variables
        try:
            conn = psycopg2.connect(
                host=os.environ.get('DB_HOST', 'localhost'),
                port=os.environ.get('DB_PORT', '5432'),
                database=os.environ.get('DB_NAME', 'vierla_db'),
                user=os.environ.get('DB_USER', 'vierla_user'),
                password=os.environ.get('DB_PASSWORD', 'vierla_password'),
                connect_timeout=5
            )
            conn.close()
            postgresql_available = True
            connection_error = None
        except Exception as e:
            postgresql_available = False
            connection_error = str(e)
        
        # Document the current state
        print(f"\n=== DATABASE CONFIGURATION ANALYSIS ===")
        print(f"PostgreSQL Available: {postgresql_available}")
        if not postgresql_available:
            print(f"Connection Error: {connection_error}")
        
        # Check environment variables
        print(f"\nEnvironment Variables:")
        env_vars = ['DB_HOST', 'DB_USER', 'DB_NAME', 'DB_PORT', 'USE_SQLITE']
        for var in env_vars:
            value = os.environ.get(var, 'NOT_SET')
            print(f"  {var}: {value}")
        
        # This test documents the current state - it may pass or fail
        # The important thing is to capture the analysis
        if not postgresql_available:
            self.fail(f"PostgreSQL connection failed: {connection_error}")
    
    def test_settings_structure_comparison_with_reference(self):
        """
        Compare current settings structure with reference architecture
        """
        # Import current development settings
        from vierla_project.settings import development
        
        # Check if settings have required structure
        self.assertTrue(hasattr(development, 'DATABASES'))
        
        db_config = development.DATABASES['default']
        
        # Compare with reference architecture requirements
        reference_requirements = {
            'ENGINE': 'django.db.backends.postgresql',
            'CONN_MAX_AGE': 600,
            'CONN_HEALTH_CHECKS': True,
            'OPTIONS': {
                'sslmode': 'prefer',
                'connect_timeout': 10,
                'options': '-c default_transaction_isolation=read_committed'
            }
        }
        
        print(f"\n=== SETTINGS STRUCTURE ANALYSIS ===")
        print(f"Current Database Engine: {db_config.get('ENGINE', 'NOT_SET')}")
        print(f"Expected Database Engine: {reference_requirements['ENGINE']}")
        
        # Check optimization settings
        optimization_settings = ['CONN_MAX_AGE', 'CONN_HEALTH_CHECKS', 'OPTIONS']
        for setting in optimization_settings:
            current_value = db_config.get(setting, 'NOT_SET')
            expected_value = reference_requirements.get(setting, 'NOT_DEFINED')
            print(f"{setting}: Current={current_value}, Expected={expected_value}")
        
        # Document gaps
        gaps = []
        if db_config.get('ENGINE') != reference_requirements['ENGINE']:
            gaps.append(f"Database engine mismatch: {db_config.get('ENGINE')} vs {reference_requirements['ENGINE']}")
        
        if 'OPTIONS' in db_config and 'OPTIONS' in reference_requirements:
            current_options = db_config['OPTIONS']
            expected_options = reference_requirements['OPTIONS']
            for key, expected_val in expected_options.items():
                if key not in current_options:
                    gaps.append(f"Missing OPTIONS.{key}: expected {expected_val}")
                elif current_options[key] != expected_val:
                    gaps.append(f"OPTIONS.{key} mismatch: {current_options[key]} vs {expected_val}")
        
        print(f"\nConfiguration Gaps Identified:")
        for gap in gaps:
            print(f"  - {gap}")
        
        return gaps
    
    def test_environment_based_settings_structure(self):
        """
        Test that environment-based settings structure matches reference
        """
        # Check if base.py exists and has proper structure
        base_settings_path = Path(__file__).parent / 'vierla_project' / 'settings' / 'base.py'
        self.assertTrue(base_settings_path.exists(), "base.py settings file should exist")
        
        # Check if environment-specific files exist
        settings_dir = base_settings_path.parent
        required_files = ['development.py', 'production.py', 'testing.py']
        
        for file_name in required_files:
            file_path = settings_dir / file_name
            self.assertTrue(file_path.exists(), f"{file_name} should exist in settings directory")
        
        # Check __init__.py for environment switching logic
        init_file = settings_dir / '__init__.py'
        self.assertTrue(init_file.exists(), "__init__.py should exist for environment switching")
        
        with open(init_file, 'r') as f:
            init_content = f.read()
        
        self.assertIn('DJANGO_ENVIRONMENT', init_content, 
                     "Environment switching should use DJANGO_ENVIRONMENT variable")
    
    def test_identify_postgresql_service_status(self):
        """
        Test to identify if PostgreSQL service is running and accessible
        """
        import subprocess
        import platform
        
        print(f"\n=== POSTGRESQL SERVICE STATUS ANALYSIS ===")
        
        # Check if PostgreSQL service is running (platform-specific)
        system = platform.system().lower()
        
        try:
            if system == 'windows':
                # Check Windows services
                result = subprocess.run(['sc', 'query', 'postgresql-x64-17'], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    print("PostgreSQL service found in Windows services")
                    if 'RUNNING' in result.stdout:
                        print("PostgreSQL service is RUNNING")
                    else:
                        print("PostgreSQL service is NOT RUNNING")
                else:
                    print("PostgreSQL service not found in Windows services")
            
            elif system == 'linux':
                # Check systemd services
                result = subprocess.run(['systemctl', 'is-active', 'postgresql'], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    print(f"PostgreSQL service status: {result.stdout.strip()}")
                else:
                    print("PostgreSQL service not found or not active")
            
            elif system == 'darwin':  # macOS
                # Check brew services
                result = subprocess.run(['brew', 'services', 'list'], 
                                      capture_output=True, text=True, timeout=10)
                if 'postgresql' in result.stdout:
                    print("PostgreSQL found in brew services")
                else:
                    print("PostgreSQL not found in brew services")
        
        except subprocess.TimeoutExpired:
            print("Service check timed out")
        except FileNotFoundError:
            print("Service management tools not found")
        except Exception as e:
            print(f"Error checking service status: {e}")
        
        # Try to connect to PostgreSQL port
        import socket
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex(('localhost', 5432))
            sock.close()
            
            if result == 0:
                print("PostgreSQL port 5432 is accessible")
            else:
                print("PostgreSQL port 5432 is NOT accessible")
        except Exception as e:
            print(f"Error checking PostgreSQL port: {e}")


if __name__ == '__main__':
    # Run the analysis tests
    unittest.main(verbosity=2)
