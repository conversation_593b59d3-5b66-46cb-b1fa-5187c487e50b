"""
Test suite for environment-based database configuration setup.

This test file is part of EPIC-AUDIT-001 - Database Configuration Overhaul
Task: Set Up Environment-Based Database Configuration
"""

import os
import sys
import unittest
from unittest.mock import patch, MagicMock
from pathlib import Path
import tempfile
import shutil

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

class EnvironmentConfigurationTest(unittest.TestCase):
    """
    Test environment-based database configuration setup and loading.
    """
    
    def setUp(self):
        """Set up test environment"""
        self.backend_dir = Path(__file__).parent
        self.env_file = self.backend_dir / '.env'
        self.original_env = dict(os.environ)
        
    def tearDown(self):
        """Clean up test environment"""
        # Restore original environment
        os.environ.clear()
        os.environ.update(self.original_env)
    
    def test_dotenv_loading_mechanism(self):
        """
        Test that dotenv loading mechanism works correctly
        """
        # Create a temporary .env file for testing
        test_env_content = """
# Test environment variables
TEST_DB_NAME=test_vierla_db
TEST_DB_USER=test_vierla_user
TEST_DB_PASSWORD=test_vierla_password
TEST_DB_HOST=test_localhost
TEST_DB_PORT=5433
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.env', delete=False) as temp_env:
            temp_env.write(test_env_content)
            temp_env_path = temp_env.name
        
        try:
            # Test dotenv loading
            from dotenv import load_dotenv
            load_dotenv(temp_env_path)
            
            # Verify variables are loaded
            self.assertEqual(os.environ.get('TEST_DB_NAME'), 'test_vierla_db')
            self.assertEqual(os.environ.get('TEST_DB_USER'), 'test_vierla_user')
            self.assertEqual(os.environ.get('TEST_DB_PASSWORD'), 'test_vierla_password')
            self.assertEqual(os.environ.get('TEST_DB_HOST'), 'test_localhost')
            self.assertEqual(os.environ.get('TEST_DB_PORT'), '5433')
            
        finally:
            # Clean up temporary file
            os.unlink(temp_env_path)
    
    def test_current_env_file_loading(self):
        """
        Test that the current .env file is being loaded correctly
        """
        # Import base settings to trigger dotenv loading
        from vierla_project.settings import base
        
        # Check if environment variables from .env are loaded
        expected_vars = {
            'SECRET_KEY': 'dev-secret-key-change-in-production',
            'DEBUG': 'True',
            'USE_SQLITE': 'false',
            'DB_NAME': 'vierla_db',
            'DB_USER': 'vierla_user',
            'DB_PASSWORD': 'vierla_password',
            'DB_HOST': 'localhost',
            'DB_PORT': '5432',
            'DJANGO_ENVIRONMENT': 'development'
        }
        
        print(f"\n=== ENVIRONMENT VARIABLE LOADING TEST ===")
        missing_vars = []
        incorrect_vars = []
        
        for var, expected_value in expected_vars.items():
            actual_value = os.environ.get(var)
            print(f"{var}: Expected='{expected_value}', Actual='{actual_value}'")
            
            if actual_value is None:
                missing_vars.append(var)
            elif actual_value != expected_value:
                incorrect_vars.append(f"{var}: expected '{expected_value}', got '{actual_value}'")
        
        if missing_vars:
            self.fail(f"Missing environment variables: {missing_vars}")
        
        if incorrect_vars:
            self.fail(f"Incorrect environment variables: {incorrect_vars}")
    
    def test_environment_based_settings_switching(self):
        """
        Test that environment-based settings switching works correctly
        """
        # Test development environment
        with patch.dict(os.environ, {'DJANGO_ENVIRONMENT': 'development'}):
            # Clear module cache to force reimport
            if 'vierla_project.settings' in sys.modules:
                del sys.modules['vierla_project.settings']
            if 'vierla_project.settings.development' in sys.modules:
                del sys.modules['vierla_project.settings.development']
            
            from vierla_project.settings import development
            self.assertTrue(hasattr(development, 'DATABASES'))
            self.assertTrue(development.DEBUG)
        
        # Test production environment
        with patch.dict(os.environ, {'DJANGO_ENVIRONMENT': 'production'}):
            # Clear module cache to force reimport
            if 'vierla_project.settings' in sys.modules:
                del sys.modules['vierla_project.settings']
            if 'vierla_project.settings.production' in sys.modules:
                del sys.modules['vierla_project.settings.production']
            
            from vierla_project.settings import production
            self.assertTrue(hasattr(production, 'DATABASES'))
            self.assertFalse(production.DEBUG)
    
    def test_database_configuration_with_environment_variables(self):
        """
        Test that database configuration uses environment variables correctly
        """
        # Set test environment variables
        test_env = {
            'DB_NAME': 'test_db_name',
            'DB_USER': 'test_db_user',
            'DB_PASSWORD': 'test_db_password',
            'DB_HOST': 'test_db_host',
            'DB_PORT': '5433',
            'USE_SQLITE': 'false'
        }
        
        with patch.dict(os.environ, test_env):
            # Clear module cache to force reimport
            if 'vierla_project.settings.development' in sys.modules:
                del sys.modules['vierla_project.settings.development']
            
            from vierla_project.settings import development
            
            db_config = development.DATABASES['default']
            
            # Verify database configuration uses environment variables
            self.assertEqual(db_config['NAME'], 'test_db_name')
            self.assertEqual(db_config['USER'], 'test_db_user')
            self.assertEqual(db_config['PASSWORD'], 'test_db_password')
            self.assertEqual(db_config['HOST'], 'test_db_host')
            self.assertEqual(db_config['PORT'], '5433')
    
    def test_postgresql_connection_with_correct_environment(self):
        """
        Test PostgreSQL connection function with correct environment variables
        """
        # Mock successful PostgreSQL connection
        with patch('psycopg2.connect') as mock_connect:
            mock_conn = MagicMock()
            mock_connect.return_value = mock_conn
            
            # Set environment variables
            test_env = {
                'DB_NAME': 'test_vierla_db',
                'DB_USER': 'test_vierla_user',
                'DB_PASSWORD': 'test_vierla_password',
                'DB_HOST': 'localhost',
                'DB_PORT': '5432'
            }
            
            with patch.dict(os.environ, test_env):
                from vierla_project.settings.development import test_postgresql_connection
                
                result = test_postgresql_connection()
                self.assertTrue(result)
                
                # Verify connection was called with correct parameters
                mock_connect.assert_called_once()
                call_kwargs = mock_connect.call_args[1]
                
                self.assertEqual(call_kwargs['database'], 'test_vierla_db')
                self.assertEqual(call_kwargs['user'], 'test_vierla_user')
                self.assertEqual(call_kwargs['password'], 'test_vierla_password')
                self.assertEqual(call_kwargs['host'], 'localhost')
                self.assertEqual(call_kwargs['port'], '5432')
    
    def test_sqlite_fallback_behavior(self):
        """
        Test that SQLite fallback works when PostgreSQL is unavailable
        """
        # Set USE_SQLITE to true
        with patch.dict(os.environ, {'USE_SQLITE': 'true'}):
            # Clear module cache to force reimport
            if 'vierla_project.settings.development' in sys.modules:
                del sys.modules['vierla_project.settings.development']
            
            from vierla_project.settings import development
            
            db_config = development.DATABASES['default']
            
            # Verify SQLite configuration
            self.assertEqual(db_config['ENGINE'], 'django.db.backends.sqlite3')
            self.assertIn('db.sqlite3', db_config['NAME'])
    
    def test_environment_file_path_resolution(self):
        """
        Test that .env file path is resolved correctly from base.py
        """
        from vierla_project.settings.base import BASE_DIR
        
        expected_env_path = BASE_DIR / '.env'
        self.assertTrue(expected_env_path.exists(), 
                       f".env file should exist at {expected_env_path}")
        
        # Verify BASE_DIR points to the correct location
        self.assertTrue((BASE_DIR / 'manage.py').exists(),
                       "BASE_DIR should point to directory containing manage.py")
    
    def test_environment_variable_precedence(self):
        """
        Test that environment variables take precedence over .env file
        """
        # Set environment variable that overrides .env file
        with patch.dict(os.environ, {'DB_NAME': 'override_db_name'}):
            # Clear module cache to force reimport
            if 'vierla_project.settings.development' in sys.modules:
                del sys.modules['vierla_project.settings.development']
            
            from vierla_project.settings import development
            
            db_config = development.DATABASES['default']
            
            # Environment variable should override .env file
            self.assertEqual(db_config['NAME'], 'override_db_name')
    
    def test_missing_environment_variables_fallback(self):
        """
        Test behavior when required environment variables are missing
        """
        # Remove database environment variables
        env_without_db = {k: v for k, v in os.environ.items() 
                         if not k.startswith('DB_')}
        
        with patch.dict(os.environ, env_without_db, clear=True):
            # Clear module cache to force reimport
            if 'vierla_project.settings.development' in sys.modules:
                del sys.modules['vierla_project.settings.development']
            
            from vierla_project.settings import development
            
            db_config = development.DATABASES['default']
            
            # Should use default values when environment variables are missing
            self.assertIsNotNone(db_config['NAME'])
            self.assertIsNotNone(db_config['USER'])
            self.assertIsNotNone(db_config['PASSWORD'])
            self.assertIsNotNone(db_config['HOST'])
            self.assertIsNotNone(db_config['PORT'])


if __name__ == '__main__':
    # Run the environment configuration tests
    unittest.main(verbosity=2)
