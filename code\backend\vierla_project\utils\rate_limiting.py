"""
Advanced Rate Limiting and DDoS Protection System

This module provides comprehensive rate limiting and DDoS protection functionality
with Redis-based storage, IP blocking, and sophisticated attack detection.

Part of EPIC-AUDIT-002 - Production Security Implementation.

Features:
- Redis-based rate limiting with sliding windows
- IP-based DDoS protection and automatic blocking
- Burst protection and adaptive rate limiting
- Whitelist/blacklist management
- Attack pattern detection
- Comprehensive monitoring and alerting
"""

import logging
import time
import hashlib
import json
from typing import Dict, List, Optional, Tuple, Union
from datetime import datetime, timedelta
from django.core.cache import cache
from django.conf import settings
from django.http import JsonResponse
from django.utils import timezone
import redis
from django.core.exceptions import ImproperlyConfigured

logger = logging.getLogger(__name__)

class RateLimitExceeded(Exception):
    """Exception raised when rate limit is exceeded."""
    def __init__(self, message, retry_after=None, limit_type=None):
        self.message = message
        self.retry_after = retry_after
        self.limit_type = limit_type
        super().__init__(message)

class RedisRateLimiter:
    """
    Redis-based rate limiter with sliding window algorithm.
    """
    
    def __init__(self, redis_client=None):
        """
        Initialize the rate limiter.
        
        Args:
            redis_client: Redis client instance. If None, uses default cache.
        """
        self.redis_client = redis_client
        if not self.redis_client:
            try:
                # Try to get Redis client from cache
                if hasattr(cache, '_cache') and hasattr(cache._cache, '_client'):
                    self.redis_client = cache._cache._client
                else:
                    # Fallback to creating Redis client
                    redis_config = getattr(settings, 'REDIS_CONFIG', {
                        'host': 'localhost',
                        'port': 6379,
                        'db': 1,
                        'decode_responses': True
                    })
                    self.redis_client = redis.Redis(**redis_config)
            except Exception as e:
                logger.warning(f"Redis not available, falling back to Django cache: {e}")
                self.redis_client = None
    
    def is_allowed(self, key: str, limit: int, window: int, burst_limit: int = None) -> Tuple[bool, Dict]:
        """
        Check if request is allowed based on rate limits.
        
        Args:
            key: Unique identifier for the rate limit (e.g., IP, user ID)
            limit: Number of requests allowed in the window
            window: Time window in seconds
            burst_limit: Optional burst limit for short-term spikes
            
        Returns:
            Tuple of (is_allowed, info_dict)
        """
        now = time.time()
        
        if self.redis_client:
            return self._redis_sliding_window(key, limit, window, now, burst_limit)
        else:
            return self._cache_sliding_window(key, limit, window, now, burst_limit)
    
    def _redis_sliding_window(self, key: str, limit: int, window: int, now: float, burst_limit: int = None) -> Tuple[bool, Dict]:
        """
        Redis-based sliding window rate limiting.
        """
        try:
            pipe = self.redis_client.pipeline()
            
            # Remove old entries
            pipe.zremrangebyscore(key, 0, now - window)
            
            # Count current requests
            pipe.zcard(key)
            
            # Add current request
            pipe.zadd(key, {str(now): now})
            
            # Set expiration
            pipe.expire(key, window + 1)
            
            results = pipe.execute()
            current_count = results[1] + 1  # +1 for the request we just added
            
            # Check burst limit if specified
            if burst_limit:
                burst_window = min(60, window // 10)  # 1 minute or 10% of window
                burst_key = f"{key}:burst"
                
                pipe = self.redis_client.pipeline()
                pipe.zremrangebyscore(burst_key, 0, now - burst_window)
                pipe.zcard(burst_key)
                pipe.zadd(burst_key, {str(now): now})
                pipe.expire(burst_key, burst_window + 1)
                
                burst_results = pipe.execute()
                burst_count = burst_results[1] + 1
                
                if burst_count > burst_limit:
                    return False, {
                        'allowed': False,
                        'limit': limit,
                        'current': current_count,
                        'window': window,
                        'burst_limit': burst_limit,
                        'burst_count': burst_count,
                        'retry_after': burst_window,
                        'limit_type': 'burst'
                    }
            
            # Check main limit
            allowed = current_count <= limit
            
            return allowed, {
                'allowed': allowed,
                'limit': limit,
                'current': current_count,
                'window': window,
                'retry_after': window if not allowed else 0,
                'limit_type': 'rate' if not allowed else None
            }
            
        except Exception as e:
            logger.error(f"Redis rate limiting error: {e}")
            # Fallback to allowing request if Redis fails
            return True, {'allowed': True, 'fallback': True, 'error': str(e)}
    
    def _cache_sliding_window(self, key: str, limit: int, window: int, now: float, burst_limit: int = None) -> Tuple[bool, Dict]:
        """
        Django cache-based sliding window rate limiting (fallback).
        """
        try:
            # Get current requests
            requests = cache.get(key, [])
            
            # Remove old requests
            cutoff = now - window
            requests = [req_time for req_time in requests if req_time > cutoff]
            
            # Check burst limit
            if burst_limit:
                burst_window = min(60, window // 10)
                burst_cutoff = now - burst_window
                burst_requests = [req_time for req_time in requests if req_time > burst_cutoff]
                
                if len(burst_requests) >= burst_limit:
                    return False, {
                        'allowed': False,
                        'limit': limit,
                        'current': len(requests),
                        'window': window,
                        'burst_limit': burst_limit,
                        'burst_count': len(burst_requests),
                        'retry_after': burst_window,
                        'limit_type': 'burst'
                    }
            
            # Check main limit
            if len(requests) >= limit:
                return False, {
                    'allowed': False,
                    'limit': limit,
                    'current': len(requests),
                    'window': window,
                    'retry_after': window,
                    'limit_type': 'rate'
                }
            
            # Add current request
            requests.append(now)
            cache.set(key, requests, window + 1)
            
            return True, {
                'allowed': True,
                'limit': limit,
                'current': len(requests),
                'window': window
            }
            
        except Exception as e:
            logger.error(f"Cache rate limiting error: {e}")
            return True, {'allowed': True, 'fallback': True, 'error': str(e)}

class IPBlockManager:
    """
    Manages IP blocking for DDoS protection.
    """
    
    def __init__(self, redis_client=None):
        self.redis_client = redis_client or RedisRateLimiter().redis_client
        self.blocked_ips_key = "security:blocked_ips"
        self.whitelist_key = "security:whitelist_ips"
        self.suspicious_ips_key = "security:suspicious_ips"
    
    def is_ip_blocked(self, ip: str) -> bool:
        """Check if IP is blocked."""
        try:
            if self.redis_client:
                return self.redis_client.sismember(self.blocked_ips_key, ip)
            else:
                blocked_ips = cache.get(self.blocked_ips_key, set())
                return ip in blocked_ips
        except Exception as e:
            logger.error(f"Error checking blocked IP: {e}")
            return False
    
    def is_ip_whitelisted(self, ip: str) -> bool:
        """Check if IP is whitelisted."""
        try:
            if self.redis_client:
                return self.redis_client.sismember(self.whitelist_key, ip)
            else:
                whitelist = cache.get(self.whitelist_key, set())
                return ip in whitelist
        except Exception as e:
            logger.error(f"Error checking whitelisted IP: {e}")
            return False
    
    def block_ip(self, ip: str, reason: str = "DDoS protection", duration: int = 3600):
        """
        Block an IP address.
        
        Args:
            ip: IP address to block
            reason: Reason for blocking
            duration: Block duration in seconds (default: 1 hour)
        """
        try:
            if self.redis_client:
                # Add to blocked set
                self.redis_client.sadd(self.blocked_ips_key, ip)
                
                # Store block info
                block_info = {
                    'ip': ip,
                    'reason': reason,
                    'blocked_at': time.time(),
                    'duration': duration,
                    'expires_at': time.time() + duration
                }
                
                block_key = f"security:block_info:{ip}"
                self.redis_client.setex(block_key, duration, json.dumps(block_info))
                
                # Set expiration for the block
                self.redis_client.expire(f"security:temp_block:{ip}", duration)
            else:
                blocked_ips = cache.get(self.blocked_ips_key, set())
                blocked_ips.add(ip)
                cache.set(self.blocked_ips_key, blocked_ips, duration)
            
            logger.warning(f"IP {ip} blocked for {duration} seconds. Reason: {reason}")
            
        except Exception as e:
            logger.error(f"Error blocking IP {ip}: {e}")
    
    def unblock_ip(self, ip: str):
        """Unblock an IP address."""
        try:
            if self.redis_client:
                self.redis_client.srem(self.blocked_ips_key, ip)
                self.redis_client.delete(f"security:block_info:{ip}")
                self.redis_client.delete(f"security:temp_block:{ip}")
            else:
                blocked_ips = cache.get(self.blocked_ips_key, set())
                blocked_ips.discard(ip)
                cache.set(self.blocked_ips_key, blocked_ips, 86400)
            
            logger.info(f"IP {ip} unblocked")
            
        except Exception as e:
            logger.error(f"Error unblocking IP {ip}: {e}")
    
    def add_to_whitelist(self, ip: str):
        """Add IP to whitelist."""
        try:
            if self.redis_client:
                self.redis_client.sadd(self.whitelist_key, ip)
            else:
                whitelist = cache.get(self.whitelist_key, set())
                whitelist.add(ip)
                cache.set(self.whitelist_key, whitelist, 86400 * 7)  # 7 days
            
            logger.info(f"IP {ip} added to whitelist")
            
        except Exception as e:
            logger.error(f"Error whitelisting IP {ip}: {e}")
    
    def mark_suspicious(self, ip: str, reason: str):
        """Mark IP as suspicious for monitoring."""
        try:
            suspicious_info = {
                'ip': ip,
                'reason': reason,
                'marked_at': time.time(),
                'count': 1
            }
            
            if self.redis_client:
                key = f"security:suspicious:{ip}"
                existing = self.redis_client.get(key)
                if existing:
                    data = json.loads(existing)
                    data['count'] += 1
                    data['last_seen'] = time.time()
                    suspicious_info = data
                
                self.redis_client.setex(key, 3600, json.dumps(suspicious_info))  # 1 hour
            else:
                cache.set(f"security:suspicious:{ip}", suspicious_info, 3600)
            
            logger.warning(f"IP {ip} marked as suspicious: {reason}")
            
        except Exception as e:
            logger.error(f"Error marking IP {ip} as suspicious: {e}")

class DDoSProtector:
    """
    Advanced DDoS protection with pattern analysis.
    """
    
    def __init__(self):
        self.rate_limiter = RedisRateLimiter()
        self.ip_manager = IPBlockManager()
        
        # DDoS detection thresholds
        self.ddos_thresholds = {
            'requests_per_second': 50,
            'requests_per_minute': 1000,
            'failed_requests_ratio': 0.8,
            'unique_endpoints_threshold': 20,
            'suspicious_user_agents': [
                'bot', 'crawler', 'spider', 'scraper', 'scanner'
            ]
        }
    
    def analyze_request_pattern(self, ip: str, request_info: Dict) -> Dict:
        """
        Analyze request patterns for DDoS detection.
        
        Args:
            ip: Client IP address
            request_info: Dictionary containing request information
            
        Returns:
            Analysis result with threat level and recommendations
        """
        analysis = {
            'ip': ip,
            'threat_level': 'low',
            'reasons': [],
            'action': 'allow',
            'block_duration': 0
        }
        
        try:
            # Check if IP is whitelisted
            if self.ip_manager.is_ip_whitelisted(ip):
                analysis['action'] = 'allow'
                analysis['reasons'].append('IP whitelisted')
                return analysis
            
            # Check if IP is already blocked
            if self.ip_manager.is_ip_blocked(ip):
                analysis['threat_level'] = 'critical'
                analysis['action'] = 'block'
                analysis['reasons'].append('IP already blocked')
                return analysis
            
            # Analyze request rate
            rate_analysis = self._analyze_request_rate(ip)
            if rate_analysis['suspicious']:
                analysis['threat_level'] = 'high'
                analysis['reasons'].extend(rate_analysis['reasons'])
            
            # Analyze request patterns
            pattern_analysis = self._analyze_request_patterns(ip, request_info)
            if pattern_analysis['suspicious']:
                if analysis['threat_level'] == 'low':
                    analysis['threat_level'] = 'medium'
                analysis['reasons'].extend(pattern_analysis['reasons'])
            
            # Determine action based on threat level
            if analysis['threat_level'] == 'critical':
                analysis['action'] = 'block'
                analysis['block_duration'] = 3600  # 1 hour
            elif analysis['threat_level'] == 'high':
                analysis['action'] = 'block'
                analysis['block_duration'] = 1800  # 30 minutes
            elif analysis['threat_level'] == 'medium':
                analysis['action'] = 'throttle'
                self.ip_manager.mark_suspicious(ip, '; '.join(analysis['reasons']))
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing request pattern for IP {ip}: {e}")
            return analysis
    
    def _analyze_request_rate(self, ip: str) -> Dict:
        """Analyze request rate for suspicious patterns."""
        analysis = {'suspicious': False, 'reasons': []}
        
        try:
            # Check requests per second
            allowed, info = self.rate_limiter.is_allowed(
                f"ddos:rps:{ip}", 
                self.ddos_thresholds['requests_per_second'], 
                1  # 1 second window
            )
            
            if not allowed:
                analysis['suspicious'] = True
                analysis['reasons'].append(f"Excessive requests per second: {info['current']}")
            
            # Check requests per minute
            allowed, info = self.rate_limiter.is_allowed(
                f"ddos:rpm:{ip}", 
                self.ddos_thresholds['requests_per_minute'], 
                60  # 1 minute window
            )
            
            if not allowed:
                analysis['suspicious'] = True
                analysis['reasons'].append(f"Excessive requests per minute: {info['current']}")
            
        except Exception as e:
            logger.error(f"Error analyzing request rate for IP {ip}: {e}")
        
        return analysis
    
    def _analyze_request_patterns(self, ip: str, request_info: Dict) -> Dict:
        """Analyze request patterns for suspicious behavior."""
        analysis = {'suspicious': False, 'reasons': []}
        
        try:
            user_agent = request_info.get('user_agent', '').lower()
            endpoint = request_info.get('endpoint', '')
            method = request_info.get('method', '')
            
            # Check for suspicious user agents
            for suspicious_ua in self.ddos_thresholds['suspicious_user_agents']:
                if suspicious_ua in user_agent:
                    analysis['suspicious'] = True
                    analysis['reasons'].append(f"Suspicious user agent: {suspicious_ua}")
                    break
            
            # Check for endpoint scanning
            endpoint_key = f"ddos:endpoints:{ip}"
            if self.rate_limiter.redis_client:
                self.rate_limiter.redis_client.sadd(endpoint_key, endpoint)
                self.rate_limiter.redis_client.expire(endpoint_key, 300)  # 5 minutes
                
                unique_endpoints = self.rate_limiter.redis_client.scard(endpoint_key)
                if unique_endpoints > self.ddos_thresholds['unique_endpoints_threshold']:
                    analysis['suspicious'] = True
                    analysis['reasons'].append(f"Endpoint scanning detected: {unique_endpoints} unique endpoints")
            
        except Exception as e:
            logger.error(f"Error analyzing request patterns for IP {ip}: {e}")
        
        return analysis

# Global instances
rate_limiter = RedisRateLimiter()
ip_manager = IPBlockManager()
ddos_protector = DDoSProtector()
