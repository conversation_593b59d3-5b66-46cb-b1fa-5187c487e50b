"""
Test suite for PostgreSQL database and user setup.

This test file is part of EPIC-AUDIT-001 - Database Configuration Overhaul
Task: Configure PostgreSQL Connection Settings
"""

import os
import sys
import unittest
from unittest.mock import patch, MagicMock
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from pathlib import Path
import subprocess

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

class PostgreSQLSetupTest(unittest.TestCase):
    """
    Test PostgreSQL database and user setup for Vierla application.
    """
    
    def setUp(self):
        """Set up test environment"""
        self.db_name = os.environ.get('DB_NAME', 'vierla_db')
        self.db_user = os.environ.get('DB_USER', 'vierla_user')
        self.db_password = os.environ.get('DB_PASSWORD', 'vierla_password')
        self.db_host = os.environ.get('DB_HOST', 'localhost')
        self.db_port = os.environ.get('DB_PORT', '5432')
        
    def test_postgresql_service_accessibility(self):
        """
        Test that PostgreSQL service is running and accessible
        """
        try:
            # Try to connect to PostgreSQL as postgres user (default superuser)
            conn = psycopg2.connect(
                host=self.db_host,
                port=self.db_port,
                user='postgres',
                password='postgres',  # Common default password
                database='postgres',  # Default database
                connect_timeout=5
            )
            conn.close()
            postgresql_accessible = True
            connection_error = None
        except Exception as e:
            postgresql_accessible = False
            connection_error = str(e)
        
        print(f"\n=== POSTGRESQL SERVICE ACCESSIBILITY ===")
        print(f"PostgreSQL Accessible: {postgresql_accessible}")
        if not postgresql_accessible:
            print(f"Connection Error: {connection_error}")
        
        self.assertTrue(postgresql_accessible, 
                       f"PostgreSQL service should be accessible: {connection_error}")
    
    def test_create_vierla_database_and_user(self):
        """
        Test creating the Vierla database and user if they don't exist
        """
        print(f"\n=== CREATING VIERLA DATABASE AND USER ===")
        
        try:
            # Connect as postgres superuser
            conn = psycopg2.connect(
                host=self.db_host,
                port=self.db_port,
                user='postgres',
                password='postgres',
                database='postgres',
                connect_timeout=5
            )
            conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            cursor = conn.cursor()
            
            # Check if user exists
            cursor.execute(
                "SELECT 1 FROM pg_roles WHERE rolname = %s",
                (self.db_user,)
            )
            user_exists = cursor.fetchone() is not None
            
            if not user_exists:
                print(f"Creating user: {self.db_user}")
                cursor.execute(
                    f"CREATE USER {self.db_user} WITH PASSWORD %s",
                    (self.db_password,)
                )
                print(f"✅ User {self.db_user} created successfully")
            else:
                print(f"✅ User {self.db_user} already exists")
            
            # Check if database exists
            cursor.execute(
                "SELECT 1 FROM pg_database WHERE datname = %s",
                (self.db_name,)
            )
            db_exists = cursor.fetchone() is not None
            
            if not db_exists:
                print(f"Creating database: {self.db_name}")
                cursor.execute(f"CREATE DATABASE {self.db_name} OWNER {self.db_user}")
                print(f"✅ Database {self.db_name} created successfully")
            else:
                print(f"✅ Database {self.db_name} already exists")
            
            # Grant privileges
            cursor.execute(f"GRANT ALL PRIVILEGES ON DATABASE {self.db_name} TO {self.db_user}")
            print(f"✅ Privileges granted to {self.db_user} on {self.db_name}")
            
            cursor.close()
            conn.close()
            
            setup_successful = True
            
        except Exception as e:
            setup_successful = False
            error_message = str(e)
            print(f"❌ Database setup failed: {error_message}")
            
            # Try alternative postgres passwords
            alternative_passwords = ['', 'admin', 'password', '123456']
            for alt_password in alternative_passwords:
                try:
                    print(f"Trying alternative postgres password: '{alt_password}'")
                    conn = psycopg2.connect(
                        host=self.db_host,
                        port=self.db_port,
                        user='postgres',
                        password=alt_password,
                        database='postgres',
                        connect_timeout=5
                    )
                    conn.close()
                    print(f"✅ Connected with postgres password: '{alt_password}'")
                    
                    # Retry setup with correct password
                    return self._setup_database_with_password(alt_password)
                    
                except Exception:
                    continue
            
            self.fail(f"Could not set up PostgreSQL database: {error_message}")
        
        return setup_successful
    
    def _setup_database_with_password(self, postgres_password):
        """
        Helper method to set up database with specific postgres password
        """
        try:
            conn = psycopg2.connect(
                host=self.db_host,
                port=self.db_port,
                user='postgres',
                password=postgres_password,
                database='postgres',
                connect_timeout=5
            )
            conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            cursor = conn.cursor()
            
            # Create user if not exists
            cursor.execute(
                "SELECT 1 FROM pg_roles WHERE rolname = %s",
                (self.db_user,)
            )
            if not cursor.fetchone():
                cursor.execute(
                    f"CREATE USER {self.db_user} WITH PASSWORD %s",
                    (self.db_password,)
                )
                print(f"✅ User {self.db_user} created")
            
            # Create database if not exists
            cursor.execute(
                "SELECT 1 FROM pg_database WHERE datname = %s",
                (self.db_name,)
            )
            if not cursor.fetchone():
                cursor.execute(f"CREATE DATABASE {self.db_name} OWNER {self.db_user}")
                print(f"✅ Database {self.db_name} created")
            
            # Grant privileges
            cursor.execute(f"GRANT ALL PRIVILEGES ON DATABASE {self.db_name} TO {self.db_user}")
            print(f"✅ Privileges granted")
            
            cursor.close()
            conn.close()
            return True
            
        except Exception as e:
            print(f"❌ Setup failed with password '{postgres_password}': {e}")
            return False
    
    def test_vierla_user_connection(self):
        """
        Test that the Vierla user can connect to the Vierla database
        """
        print(f"\n=== TESTING VIERLA USER CONNECTION ===")
        
        try:
            conn = psycopg2.connect(
                host=self.db_host,
                port=self.db_port,
                user=self.db_user,
                password=self.db_password,
                database=self.db_name,
                connect_timeout=5
            )
            
            # Test basic operations
            cursor = conn.cursor()
            cursor.execute("SELECT version()")
            version = cursor.fetchone()[0]
            print(f"✅ Connected successfully to PostgreSQL")
            print(f"PostgreSQL Version: {version}")
            
            # Test table creation permissions
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS test_connection (
                    id SERIAL PRIMARY KEY,
                    test_field VARCHAR(100)
                )
            """)
            
            cursor.execute("INSERT INTO test_connection (test_field) VALUES (%s)", ("test_value",))
            cursor.execute("SELECT test_field FROM test_connection WHERE test_field = %s", ("test_value",))
            result = cursor.fetchone()
            
            self.assertEqual(result[0], "test_value")
            print("✅ Database operations successful")
            
            # Clean up test table
            cursor.execute("DROP TABLE test_connection")
            
            cursor.close()
            conn.close()
            
            connection_successful = True
            
        except Exception as e:
            connection_successful = False
            error_message = str(e)
            print(f"❌ Vierla user connection failed: {error_message}")
        
        self.assertTrue(connection_successful, 
                       f"Vierla user should be able to connect to database: {error_message}")
    
    def test_django_postgresql_connection(self):
        """
        Test that Django can connect to PostgreSQL with current configuration
        """
        print(f"\n=== TESTING DJANGO POSTGRESQL CONNECTION ===")
        
        # Import Django settings to test connection
        from vierla_project.settings.development import test_postgresql_connection
        
        result = test_postgresql_connection()
        
        print(f"Django PostgreSQL Connection: {'✅ Success' if result else '❌ Failed'}")
        
        self.assertTrue(result, "Django should be able to connect to PostgreSQL")
    
    def test_postgresql_configuration_optimization(self):
        """
        Test PostgreSQL configuration for optimization settings
        """
        print(f"\n=== TESTING POSTGRESQL OPTIMIZATION SETTINGS ===")
        
        try:
            conn = psycopg2.connect(
                host=self.db_host,
                port=self.db_port,
                user=self.db_user,
                password=self.db_password,
                database=self.db_name,
                connect_timeout=5
            )
            
            cursor = conn.cursor()
            
            # Test connection settings
            cursor.execute("SHOW default_transaction_isolation")
            isolation_level = cursor.fetchone()[0]
            print(f"Transaction Isolation Level: {isolation_level}")
            
            cursor.execute("SHOW max_connections")
            max_connections = cursor.fetchone()[0]
            print(f"Max Connections: {max_connections}")
            
            cursor.execute("SHOW shared_buffers")
            shared_buffers = cursor.fetchone()[0]
            print(f"Shared Buffers: {shared_buffers}")
            
            cursor.close()
            conn.close()
            
            print("✅ PostgreSQL optimization settings verified")
            
        except Exception as e:
            print(f"❌ Could not verify optimization settings: {e}")
            self.fail(f"PostgreSQL optimization verification failed: {e}")
    
    def test_generate_database_setup_script(self):
        """
        Generate a database setup script for manual execution if needed
        """
        setup_script = f"""
-- PostgreSQL Database Setup Script for Vierla Application
-- Run this script as PostgreSQL superuser (postgres)

-- Create user if not exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = '{self.db_user}') THEN
        CREATE USER {self.db_user} WITH PASSWORD '{self.db_password}';
        RAISE NOTICE 'User {self.db_user} created';
    ELSE
        RAISE NOTICE 'User {self.db_user} already exists';
    END IF;
END
$$;

-- Create database if not exists
SELECT 'CREATE DATABASE {self.db_name} OWNER {self.db_user}'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = '{self.db_name}')\\gexec

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE {self.db_name} TO {self.db_user};

-- Verify setup
\\c {self.db_name}
SELECT 'Database setup completed successfully for {self.db_name}' as status;
"""
        
        script_path = Path(__file__).parent / 'setup_postgresql_database.sql'
        with open(script_path, 'w') as f:
            f.write(setup_script)
        
        print(f"\n=== DATABASE SETUP SCRIPT GENERATED ===")
        print(f"Script saved to: {script_path}")
        print("To run manually:")
        print(f"psql -U postgres -h {self.db_host} -p {self.db_port} -f {script_path}")
        
        self.assertTrue(script_path.exists(), "Setup script should be generated")


if __name__ == '__main__':
    # Run the PostgreSQL setup tests
    unittest.main(verbosity=2)
