/**
 * Customer Stack Navigator
 * Role-based navigation stack for customer users
 * Implements lazy loading and FSM-based navigation patterns
 */

import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';

// Import customer-specific screens
import { HomeScreen } from '../screens/main/HomeScreen';
import { ServicesScreen } from '../screens/main/ServicesScreen';
import { BookingsScreen } from '../screens/main/BookingsScreen';
import { ProfileScreen } from '../screens/main/ProfileScreen';
import { SettingsScreen } from '../screens/main/SettingsScreen';

// Import lazy-loaded screens
import { LazyServiceDetailsScreen } from '../components/lazy/LazyScreens';
import { LazyBookingDetailsScreen } from '../components/lazy/LazyScreens';
import { LazyEditProfileScreen } from '../components/lazy/LazyScreens';
import { LazyNotificationsScreen } from '../components/lazy/LazyScreens';

// Import customer tabs
import { CustomerTabs } from './CustomerTabs';

// Navigation types
export type CustomerStackParamList = {
  CustomerTabs: undefined;
  ServiceDetails: { serviceId: string };
  BookingDetails: { bookingId: string };
  EditProfile: undefined;
  Notifications: undefined;
  Settings: undefined;
};

const Stack = createStackNavigator<CustomerStackParamList>();

export const CustomerStack: React.FC = () => {
  return (
    <Stack.Navigator
      initialRouteName="CustomerTabs"
      screenOptions={{
        headerShown: false,
        cardStyle: { backgroundColor: '#FFFFFF' },
        animationEnabled: true,
        presentation: 'card',
      }}
    >
      {/* Main customer tabs */}
      <Stack.Screen
        name="CustomerTabs"
        component={CustomerTabs}
        options={{
          headerShown: false,
        }}
      />

      {/* Service details screen */}
      <Stack.Screen
        name="ServiceDetails"
        component={LazyServiceDetailsScreen}
        options={{
          headerShown: true,
          title: 'Service Details',
          headerBackTitleVisible: false,
        }}
      />

      {/* Booking details screen */}
      <Stack.Screen
        name="BookingDetails"
        component={LazyBookingDetailsScreen}
        options={{
          headerShown: true,
          title: 'Booking Details',
          headerBackTitleVisible: false,
        }}
      />

      {/* Edit profile screen */}
      <Stack.Screen
        name="EditProfile"
        component={LazyEditProfileScreen}
        options={{
          headerShown: true,
          title: 'Edit Profile',
          headerBackTitleVisible: false,
        }}
      />

      {/* Notifications screen */}
      <Stack.Screen
        name="Notifications"
        component={LazyNotificationsScreen}
        options={{
          headerShown: true,
          title: 'Notifications',
          headerBackTitleVisible: false,
        }}
      />

      {/* Settings screen */}
      <Stack.Screen
        name="Settings"
        component={SettingsScreen}
        options={{
          headerShown: true,
          title: 'Settings',
          headerBackTitleVisible: false,
        }}
      />
    </Stack.Navigator>
  );
};
