/**
 * Provider Analytics Screen
 * Analytics and insights for service providers
 */

import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export const ProviderAnalyticsScreen: React.FC = () => {
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content}>
        <Text style={styles.title}>Analytics</Text>
        <Text style={styles.subtitle}>Insights and performance metrics</Text>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Analytics Overview</Text>
          <Text style={styles.text}>
            This is a placeholder for the provider analytics screen. 
            This screen will display detailed analytics and insights for service providers.
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Key Metrics</Text>
          <View style={styles.metricsContainer}>
            <View style={styles.metricCard}>
              <Text style={styles.metricValue}>$0</Text>
              <Text style={styles.metricLabel}>Total Revenue</Text>
            </View>
            <View style={styles.metricCard}>
              <Text style={styles.metricValue}>0</Text>
              <Text style={styles.metricLabel}>Completed Jobs</Text>
            </View>
          </View>
          <View style={styles.metricsContainer}>
            <View style={styles.metricCard}>
              <Text style={styles.metricValue}>0.0</Text>
              <Text style={styles.metricLabel}>Average Rating</Text>
            </View>
            <View style={styles.metricCard}>
              <Text style={styles.metricValue}>0%</Text>
              <Text style={styles.metricLabel}>Response Rate</Text>
            </View>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Features</Text>
          <Text style={styles.text}>• Revenue tracking and trends</Text>
          <Text style={styles.text}>• Booking conversion rates</Text>
          <Text style={styles.text}>• Customer satisfaction metrics</Text>
          <Text style={styles.text}>• Service performance analysis</Text>
          <Text style={styles.text}>• Competitive insights</Text>
          <Text style={styles.text}>• Growth recommendations</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    marginBottom: 24,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 12,
  },
  text: {
    fontSize: 16,
    color: '#333333',
    lineHeight: 24,
    marginBottom: 8,
  },
  metricsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  metricCard: {
    flex: 1,
    backgroundColor: '#F8F9FA',
    padding: 16,
    borderRadius: 8,
    marginHorizontal: 4,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  metricValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 4,
  },
  metricLabel: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'center',
  },
});

export default ProviderAnalyticsScreen;
