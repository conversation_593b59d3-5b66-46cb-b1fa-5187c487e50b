"""
Rate Limiting Decorators

This module provides decorators for applying rate limiting to specific
views and API endpoints with customizable limits and behavior.

Part of EPIC-AUDIT-002 - Production Security Implementation.

Features:
- View-specific rate limiting decorators
- Customizable rate limits per endpoint
- User-tier based rate limiting
- IP and user-based rate limiting
- Burst protection decorators
- DDoS protection decorators
"""

import logging
import functools
from typing import Dict, Optional, Union, Callable
from django.http import JsonResponse
from django.contrib.auth.models import AnonymousUser
from django.views.decorators.cache import never_cache
from django.utils.decorators import method_decorator
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from vierla_project.utils.rate_limiting import (
    rate_limiter, 
    ip_manager, 
    ddos_protector,
    RateLimitExceeded
)

logger = logging.getLogger(__name__)

def rate_limit(
    limit: int = 100,
    window: int = 3600,
    burst_limit: Optional[int] = None,
    per_user: bool = True,
    per_ip: bool = True,
    key_func: Optional[Callable] = None,
    skip_if_authenticated: bool = False,
    user_tiers: Optional[Dict[str, Dict]] = None
):
    """
    Rate limiting decorator for views and API endpoints.
    
    Args:
        limit: Number of requests allowed in the window
        window: Time window in seconds
        burst_limit: Optional burst limit for short-term spikes
        per_user: Apply rate limiting per authenticated user
        per_ip: Apply rate limiting per IP address
        key_func: Custom function to generate rate limiting key
        skip_if_authenticated: Skip rate limiting for authenticated users
        user_tiers: Different limits for different user tiers
    
    Example:
        @rate_limit(limit=10, window=600, burst_limit=5)
        def login_view(request):
            # Login logic here
            pass
    """
    def decorator(view_func):
        @functools.wraps(view_func)
        def wrapper(request, *args, **kwargs):
            try:
                # Get client information
                client_ip = _get_client_ip(request)
                user = getattr(request, 'user', AnonymousUser())
                
                # Skip if IP is whitelisted
                if ip_manager.is_ip_whitelisted(client_ip):
                    return view_func(request, *args, **kwargs)
                
                # Check if IP is blocked
                if ip_manager.is_ip_blocked(client_ip):
                    return _create_blocked_response()
                
                # Skip rate limiting for authenticated users if specified
                if skip_if_authenticated and not isinstance(user, AnonymousUser):
                    return view_func(request, *args, **kwargs)
                
                # Determine rate limits based on user tier
                current_limit = limit
                current_window = window
                current_burst = burst_limit
                
                if user_tiers and not isinstance(user, AnonymousUser):
                    user_tier = _get_user_tier(user)
                    if user_tier in user_tiers:
                        tier_config = user_tiers[user_tier]
                        current_limit = tier_config.get('limit', limit)
                        current_window = tier_config.get('window', window)
                        current_burst = tier_config.get('burst_limit', burst_limit)
                
                # Generate rate limiting key
                if key_func:
                    rate_key = key_func(request, *args, **kwargs)
                else:
                    rate_key = _generate_rate_key(request, view_func.__name__, per_user, per_ip)
                
                # Check rate limits
                allowed, info = rate_limiter.is_allowed(
                    rate_key,
                    current_limit,
                    current_window,
                    current_burst
                )
                
                if not allowed:
                    return _create_rate_limit_response(info, view_func.__name__)
                
                # Execute the view
                response = view_func(request, *args, **kwargs)
                
                # Add rate limiting headers to response
                if hasattr(response, '__setitem__'):  # Check if response supports headers
                    _add_rate_limit_headers(response, info)
                
                return response
                
            except Exception as e:
                logger.error(f"Error in rate limiting decorator: {e}")
                # Allow request to proceed if rate limiting fails
                return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator

def authentication_rate_limit(
    login_limit: int = 5,
    login_window: int = 900,
    failed_limit: int = 3,
    failed_window: int = 300,
    block_duration: int = 1800
):
    """
    Specialized rate limiting decorator for authentication endpoints.
    
    Args:
        login_limit: Number of login attempts allowed
        login_window: Time window for login attempts (seconds)
        failed_limit: Number of failed attempts before blocking
        failed_window: Time window for failed attempts (seconds)
        block_duration: Duration to block IP after limit exceeded (seconds)
    """
    def decorator(view_func):
        @functools.wraps(view_func)
        def wrapper(request, *args, **kwargs):
            try:
                client_ip = _get_client_ip(request)
                
                # Check if IP is blocked
                if ip_manager.is_ip_blocked(client_ip):
                    return _create_auth_blocked_response()
                
                # Check login attempt rate limit
                login_key = f"auth:login:{client_ip}"
                allowed, info = rate_limiter.is_allowed(login_key, login_limit, login_window)
                
                if not allowed:
                    # Block IP for authentication
                    ip_manager.block_ip(
                        client_ip,
                        "Authentication rate limit exceeded",
                        block_duration
                    )
                    return _create_auth_rate_limit_response(info)
                
                # Execute the view
                response = view_func(request, *args, **kwargs)
                
                # Track failed login attempts
                if hasattr(response, 'status_code') and response.status_code in [401, 403]:
                    failed_key = f"auth:failed:{client_ip}"
                    failed_allowed, failed_info = rate_limiter.is_allowed(
                        failed_key, failed_limit, failed_window
                    )
                    
                    if not failed_allowed:
                        # Block IP for too many failed attempts
                        ip_manager.block_ip(
                            client_ip,
                            "Too many failed authentication attempts",
                            block_duration
                        )
                        logger.warning(f"IP {client_ip} blocked for failed authentication attempts")
                
                return response
                
            except Exception as e:
                logger.error(f"Error in authentication rate limiting: {e}")
                return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator

def ddos_protection(
    enable_pattern_analysis: bool = True,
    enable_auto_block: bool = True,
    suspicious_threshold: int = 50
):
    """
    DDoS protection decorator for high-risk endpoints.
    
    Args:
        enable_pattern_analysis: Enable request pattern analysis
        enable_auto_block: Enable automatic IP blocking
        suspicious_threshold: Threshold for marking IP as suspicious
    """
    def decorator(view_func):
        @functools.wraps(view_func)
        def wrapper(request, *args, **kwargs):
            try:
                client_ip = _get_client_ip(request)
                
                # Skip if IP is whitelisted
                if ip_manager.is_ip_whitelisted(client_ip):
                    return view_func(request, *args, **kwargs)
                
                # Check if IP is blocked
                if ip_manager.is_ip_blocked(client_ip):
                    return _create_blocked_response()
                
                # DDoS pattern analysis
                if enable_pattern_analysis:
                    request_info = {
                        'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                        'endpoint': request.path,
                        'method': request.method,
                        'content_type': getattr(request, 'content_type', ''),
                    }
                    
                    analysis = ddos_protector.analyze_request_pattern(client_ip, request_info)
                    
                    if analysis['action'] == 'block' and enable_auto_block:
                        ip_manager.block_ip(
                            client_ip,
                            f"DDoS protection: {'; '.join(analysis['reasons'])}",
                            analysis['block_duration']
                        )
                        return _create_ddos_response(analysis)
                    
                    elif analysis['threat_level'] in ['medium', 'high']:
                        # Mark as suspicious but don't block yet
                        ip_manager.mark_suspicious(client_ip, '; '.join(analysis['reasons']))
                
                return view_func(request, *args, **kwargs)
                
            except Exception as e:
                logger.error(f"Error in DDoS protection: {e}")
                return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator

def burst_protection(
    burst_limit: int = 20,
    burst_window: int = 60,
    sustained_limit: int = 100,
    sustained_window: int = 3600
):
    """
    Burst protection decorator for handling traffic spikes.
    
    Args:
        burst_limit: Number of requests allowed in burst window
        burst_window: Burst time window in seconds
        sustained_limit: Number of requests allowed in sustained window
        sustained_window: Sustained time window in seconds
    """
    def decorator(view_func):
        @functools.wraps(view_func)
        def wrapper(request, *args, **kwargs):
            try:
                client_ip = _get_client_ip(request)
                base_key = f"burst:{view_func.__name__}:{client_ip}"
                
                # Check burst limit
                burst_allowed, burst_info = rate_limiter.is_allowed(
                    f"{base_key}:burst",
                    burst_limit,
                    burst_window
                )
                
                if not burst_allowed:
                    return _create_burst_limit_response(burst_info, 'burst')
                
                # Check sustained limit
                sustained_allowed, sustained_info = rate_limiter.is_allowed(
                    f"{base_key}:sustained",
                    sustained_limit,
                    sustained_window
                )
                
                if not sustained_allowed:
                    return _create_burst_limit_response(sustained_info, 'sustained')
                
                return view_func(request, *args, **kwargs)
                
            except Exception as e:
                logger.error(f"Error in burst protection: {e}")
                return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator

# Class-based view decorators
def rate_limit_class(
    limit: int = 100,
    window: int = 3600,
    methods: Optional[list] = None,
    **kwargs
):
    """
    Rate limiting decorator for class-based views.
    
    Args:
        limit: Number of requests allowed
        window: Time window in seconds
        methods: List of HTTP methods to apply rate limiting to
        **kwargs: Additional arguments for rate_limit decorator
    """
    def decorator(cls):
        original_dispatch = cls.dispatch
        
        @functools.wraps(original_dispatch)
        def dispatch_wrapper(self, request, *args, **view_kwargs):
            # Apply rate limiting only to specified methods
            if methods and request.method.upper() not in [m.upper() for m in methods]:
                return original_dispatch(self, request, *args, **view_kwargs)
            
            # Apply rate limiting
            rate_limited_dispatch = rate_limit(limit=limit, window=window, **kwargs)(
                original_dispatch
            )
            return rate_limited_dispatch(self, request, *args, **view_kwargs)
        
        cls.dispatch = dispatch_wrapper
        return cls
    
    return decorator

# Helper functions
def _get_client_ip(request) -> str:
    """Get client IP address from request."""
    forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if forwarded_for:
        return forwarded_for.split(',')[0].strip()
    return request.META.get('REMOTE_ADDR', '127.0.0.1')

def _get_user_tier(user) -> str:
    """Determine user tier for rate limiting."""
    if isinstance(user, AnonymousUser):
        return 'anonymous'
    
    if hasattr(user, 'subscription') and user.subscription.is_premium:
        return 'premium'
    
    return 'authenticated'

def _generate_rate_key(request, view_name: str, per_user: bool, per_ip: bool) -> str:
    """Generate rate limiting key."""
    key_parts = [view_name]
    
    if per_ip:
        key_parts.append(_get_client_ip(request))
    
    if per_user and not isinstance(getattr(request, 'user', AnonymousUser()), AnonymousUser):
        key_parts.append(str(request.user.id))
    
    return ':'.join(key_parts)

def _create_rate_limit_response(info: Dict, view_name: str) -> JsonResponse:
    """Create rate limit exceeded response."""
    return JsonResponse({
        'error': 'Rate limit exceeded',
        'message': f'Too many requests to {view_name}',
        'retry_after': info.get('retry_after', 3600),
        'limit': info.get('limit', 0),
        'current': info.get('current', 0)
    }, status=429)

def _create_auth_blocked_response() -> JsonResponse:
    """Create authentication blocked response."""
    return JsonResponse({
        'error': 'Authentication blocked',
        'message': 'IP temporarily blocked due to suspicious authentication activity',
        'retry_after': 1800
    }, status=403)

def _create_auth_rate_limit_response(info: Dict) -> JsonResponse:
    """Create authentication rate limit response."""
    return JsonResponse({
        'error': 'Authentication rate limit exceeded',
        'message': 'Too many authentication attempts',
        'retry_after': info.get('retry_after', 900),
        'limit': info.get('limit', 0)
    }, status=429)

def _create_blocked_response() -> JsonResponse:
    """Create blocked IP response."""
    return JsonResponse({
        'error': 'Access denied',
        'message': 'IP address temporarily blocked',
        'contact': 'Contact support if you believe this is an error'
    }, status=403)

def _create_ddos_response(analysis: Dict) -> JsonResponse:
    """Create DDoS protection response."""
    return JsonResponse({
        'error': 'DDoS protection activated',
        'message': 'Suspicious activity detected',
        'threat_level': analysis.get('threat_level', 'unknown')
    }, status=429)

def _create_burst_limit_response(info: Dict, limit_type: str) -> JsonResponse:
    """Create burst limit response."""
    return JsonResponse({
        'error': f'{limit_type.title()} limit exceeded',
        'message': f'Too many requests in {limit_type} window',
        'retry_after': info.get('retry_after', 60),
        'limit_type': limit_type
    }, status=429)

def _add_rate_limit_headers(response, info: Dict):
    """Add rate limiting headers to response."""
    try:
        if hasattr(response, '__setitem__'):
            response['X-RateLimit-Limit'] = str(info.get('limit', 0))
            response['X-RateLimit-Remaining'] = str(max(0, info.get('limit', 0) - info.get('current', 0)))
            response['X-RateLimit-Window'] = str(info.get('window', 3600))
    except Exception as e:
        logger.error(f"Error adding rate limit headers: {e}")
