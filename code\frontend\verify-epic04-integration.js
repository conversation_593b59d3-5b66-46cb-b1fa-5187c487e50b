#!/usr/bin/env node
/**
 * EPIC-04 Integration Verification Script
 * Tests the complete frontend-backend integration for User Profile Management
 */

const axios = require('axios');

const API_BASE_URL = 'http://************:8000/api';

async function verifyEpic04Integration() {
  console.log('🧪 EPIC-04 User Profile Management Integration Test');
  console.log('=' * 60);
  
  try {
    // Step 1: Test user registration and login
    console.log('\n1. Testing user authentication...');
    
    const testUser = {
      email: '<EMAIL>',  // Use the verified test user from backend test
      password: 'testpass123'
    };

    console.log('✅ Using existing verified test user');
    
    // Login to get token
    const loginResponse = await axios.post(`${API_BASE_URL}/auth/login/`, {
      email: testUser.email,
      password: testUser.password
    });
    
    if (loginResponse.status !== 200) {
      throw new Error('Login failed');
    }
    
    const { access: accessToken } = loginResponse.data;
    console.log('✅ User authentication successful');
    
    const headers = { Authorization: `Bearer ${accessToken}` };
    
    // Step 2: Test basic profile endpoint
    console.log('\n2. Testing basic profile endpoint...');
    
    const profileResponse = await axios.get(`${API_BASE_URL}/auth/profile/`, { headers });
    
    if (profileResponse.status === 200) {
      console.log('✅ Basic profile endpoint working');
      console.log(`📋 User: ${profileResponse.data.first_name} ${profileResponse.data.last_name}`);
    } else {
      throw new Error('Basic profile endpoint failed');
    }
    
    // Step 3: Test extended profile details endpoint (NEW)
    console.log('\n3. Testing extended profile details endpoint...');
    
    const detailsResponse = await axios.get(`${API_BASE_URL}/auth/profile/details/`, { headers });
    
    if (detailsResponse.status === 200) {
      console.log('✅ Extended profile details endpoint working');
      console.log('📋 Profile details structure:');
      console.log(`   - Country: ${detailsResponse.data.country || 'Not set'}`);
      console.log(`   - Search radius: ${detailsResponse.data.search_radius || 'Not set'}`);
      console.log(`   - Business name: ${detailsResponse.data.business_name || 'Not set'}`);
      console.log(`   - Full address: ${detailsResponse.data.full_address || 'Not set'}`);
      console.log(`   - Has location: ${detailsResponse.data.has_location}`);
    } else {
      throw new Error('Extended profile details endpoint failed');
    }
    
    // Step 4: Test profile details update
    console.log('\n4. Testing profile details update...');
    
    const updateData = {
      city: 'Toronto',
      state: 'Ontario',
      country: 'Canada',
      search_radius: 35,
      business_name: 'Epic04 Test Business',
      show_phone_publicly: true,
      auto_accept_bookings: false
    };
    
    const updateResponse = await axios.patch(
      `${API_BASE_URL}/auth/profile/details/`, 
      updateData, 
      { headers }
    );
    
    if (updateResponse.status === 200) {
      console.log('✅ Profile details update successful');
      console.log(`📋 Updated data:`);
      console.log(`   - City: ${updateResponse.data.city}`);
      console.log(`   - Search radius: ${updateResponse.data.search_radius}`);
      console.log(`   - Business name: ${updateResponse.data.business_name}`);
    } else {
      throw new Error('Profile details update failed');
    }
    
    // Step 5: Verify data persistence
    console.log('\n5. Verifying data persistence...');
    
    const verifyResponse = await axios.get(`${API_BASE_URL}/auth/profile/details/`, { headers });
    
    if (verifyResponse.status === 200) {
      const data = verifyResponse.data;
      const isDataPersisted = (
        data.city === 'Toronto' &&
        data.search_radius === 35 &&
        data.business_name === 'Epic04 Test Business'
      );
      
      if (isDataPersisted) {
        console.log('✅ Data persistence verified');
      } else {
        throw new Error('Data persistence failed');
      }
    } else {
      throw new Error('Data persistence verification failed');
    }
    
    // Step 6: Test frontend API service compatibility
    console.log('\n6. Testing frontend API service compatibility...');
    
    // Simulate the frontend profileAPI.getProfileDetails() call
    try {
      const frontendCompatResponse = await axios.get(`${API_BASE_URL}/auth/profile/details/`, { headers });
      
      // Check if response matches expected frontend UserProfile interface
      const expectedFields = [
        'address', 'city', 'state', 'zip_code', 'country',
        'latitude', 'longitude', 'full_address', 'has_location',
        'business_name', 'business_description', 'years_of_experience',
        'website', 'instagram', 'facebook',
        'search_radius', 'auto_accept_bookings',
        'show_phone_publicly', 'show_email_publicly', 'allow_reviews',
        'created_at', 'updated_at'
      ];
      
      const responseFields = Object.keys(frontendCompatResponse.data);
      const missingFields = expectedFields.filter(field => !responseFields.includes(field));
      
      if (missingFields.length === 0) {
        console.log('✅ Frontend API service compatibility verified');
        console.log('📋 All expected UserProfile fields present');
      } else {
        console.log(`⚠️  Missing fields: ${missingFields.join(', ')}`);
      }
    } catch (error) {
      throw new Error('Frontend compatibility test failed');
    }
    
    console.log('\n🎉 EPIC-04 Integration Test PASSED!');
    console.log('✅ Backend UserProfile model implemented');
    console.log('✅ Profile details endpoints working');
    console.log('✅ Frontend-backend integration complete');
    console.log('✅ Data persistence verified');
    console.log('✅ API compatibility confirmed');
    
    return true;
    
  } catch (error) {
    console.log('\n❌ EPIC-04 Integration Test FAILED!');
    console.log(`Error: ${error.message}`);
    if (error.response) {
      console.log(`Status: ${error.response.status}`);
      console.log(`Response: ${JSON.stringify(error.response.data, null, 2)}`);
    }
    return false;
  }
}

// Run the test
if (require.main === module) {
  verifyEpic04Integration()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = { verifyEpic04Integration };
