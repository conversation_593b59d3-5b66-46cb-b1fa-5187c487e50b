"""
URL configuration for vierla_project project.
Vierla Beauty Services Marketplace Backend
"""

from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    # Admin interface
    path("admin/", admin.site.urls),

    # New versioned, role-based API structure
    path('api/v1/', include('api.v1.urls')),

    # Legacy API endpoints (maintained for backward compatibility)
    path('api/auth/', include('authentication.urls')),
    path('api/catalog/', include('catalog.urls')),
    path('api/provider/', include('catalog.provider_urls')),
    path('api/bookings/', include('bookings.urls')),
    path('api/payments/', include('payments.urls')),
    path('api/messaging/', include('messaging.urls')),
    path('api/reviews/', include('reviews.urls')),
    path('api/notifications/', include('notifications.urls')),
    path('api/analytics/', include('analytics.urls')),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
