#!/usr/bin/env python
"""
Test script for profile details endpoint
Tests the new /api/auth/profile/details/ endpoint
"""

import os
import sys
import django
import requests
import json

# Setup Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'vierla_project.settings')
django.setup()

from authentication.models import User, UserProfile

def test_profile_details_endpoint():
    """Test the profile details endpoint"""
    
    API_BASE_URL = 'http://127.0.0.1:8000'
    
    print("🧪 Testing Profile Details Endpoint")
    print("=" * 50)
    
    # Test data
    test_email = '<EMAIL>'
    test_password = 'testpass123'
    
    try:
        # Step 1: Create a test user if it doesn't exist
        print("1. Setting up test user...")
        user, created = User.objects.get_or_create(
            email=test_email,
            defaults={
                'username': 'profiletest',
                'first_name': 'Profile',
                'last_name': 'Test',
                'role': 'customer'
            }
        )
        
        if created:
            user.set_password(test_password)
            user.is_verified = True  # Verify the user for testing
            user.account_status = 'active'  # Set account as active
            user.save()
            print(f"✅ Created test user: {test_email}")
        else:
            # Ensure existing user is verified
            user.is_verified = True
            user.account_status = 'active'
            user.save()
            print(f"✅ Using existing test user: {test_email}")
        
        # Step 2: Login to get token
        print("\n2. Logging in...")
        login_data = {
            'email': test_email,
            'password': test_password
        }
        
        login_response = requests.post(f'{API_BASE_URL}/api/auth/login/', json=login_data)
        
        if login_response.status_code != 200:
            print(f"❌ Login failed: {login_response.status_code}")
            print(f"Response: {login_response.text}")
            return False
            
        token_data = login_response.json()
        access_token = token_data['access']
        print("✅ Login successful")
        
        # Step 3: Test GET profile details
        print("\n3. Testing GET /api/auth/profile/details/...")
        headers = {'Authorization': f'Bearer {access_token}'}
        
        get_response = requests.get(f'{API_BASE_URL}/api/auth/profile/details/', headers=headers)
        print(f"GET Status: {get_response.status_code}")
        
        if get_response.status_code == 200:
            profile_data = get_response.json()
            print("✅ GET profile details successful")
            print(f"Profile data keys: {list(profile_data.keys())}")
            print(f"Default country: {profile_data.get('country', 'Not set')}")
            print(f"Default search_radius: {profile_data.get('search_radius', 'Not set')}")
        else:
            print(f"❌ GET profile details failed: {get_response.text}")
            return False
        
        # Step 4: Test PATCH profile details
        print("\n4. Testing PATCH /api/auth/profile/details/...")
        update_data = {
            'city': 'Toronto',
            'state': 'Ontario',
            'country': 'Canada',
            'search_radius': 30,
            'show_phone_publicly': True,
            'business_name': 'Test Business'
        }
        
        patch_response = requests.patch(
            f'{API_BASE_URL}/api/auth/profile/details/', 
            json=update_data, 
            headers=headers
        )
        print(f"PATCH Status: {patch_response.status_code}")
        
        if patch_response.status_code == 200:
            updated_data = patch_response.json()
            print("✅ PATCH profile details successful")
            print(f"Updated city: {updated_data.get('city')}")
            print(f"Updated search_radius: {updated_data.get('search_radius')}")
            print(f"Updated business_name: {updated_data.get('business_name')}")
        else:
            print(f"❌ PATCH profile details failed: {patch_response.text}")
            return False
        
        # Step 5: Verify data persistence
        print("\n5. Verifying data persistence...")
        verify_response = requests.get(f'{API_BASE_URL}/api/auth/profile/details/', headers=headers)
        
        if verify_response.status_code == 200:
            verify_data = verify_response.json()
            if (verify_data.get('city') == 'Toronto' and 
                verify_data.get('search_radius') == 30 and
                verify_data.get('business_name') == 'Test Business'):
                print("✅ Data persistence verified")
            else:
                print("❌ Data persistence failed")
                return False
        else:
            print(f"❌ Verification failed: {verify_response.text}")
            return False
        
        print("\n🎉 All tests passed! Profile details endpoint is working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with exception: {str(e)}")
        return False

if __name__ == '__main__':
    success = test_profile_details_endpoint()
    sys.exit(0 if success else 1)
