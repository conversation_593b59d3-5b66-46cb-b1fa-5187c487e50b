#!/usr/bin/env python
"""
Test suite for EPIC-AUDIT-004: API Architecture Restructuring
Tests the implementation of versioned, role-based API architecture
"""

import os
import sys
import django

# Setup Django BEFORE importing Django modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'vierla_project.settings')
django.setup()

# Now import Django modules
from django.test import TestCase, Client
from django.urls import reverse, resolve
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase, APIClient
from rest_framework import status

User = get_user_model()


class APIArchitectureRestructuringTest(APITestCase):
    """Test the new versioned, role-based API architecture"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.client = APIClient()
        
        # Create test users
        self.customer_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            username='customer_test',
            role='customer',
            is_verified=True,
            account_status='active'
        )
        
        self.provider_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            username='provider_test',
            role='service_provider',
            is_verified=True,
            account_status='active'
        )
    
    def get_auth_token(self, user):
        """Get authentication token for user"""
        response = self.client.post('/api/auth/login/', {
            'email': user.email,
            'password': 'testpass123'
        })
        return response.data['access']
    
    def test_v1_api_structure_exists(self):
        """Test that the v1 API structure is properly set up"""
        # Test that v1 API endpoints are accessible
        test_endpoints = [
            '/api/v1/customer/',
            '/api/v1/provider/',
            '/api/v1/shared/',
        ]
        
        for endpoint in test_endpoints:
            with self.subTest(endpoint=endpoint):
                response = self.client.get(endpoint)
                # Should not return 404 (endpoint exists)
                self.assertNotEqual(response.status_code, 404,
                                  f"Endpoint {endpoint} should exist")
    
    def test_customer_api_endpoints(self):
        """Test customer-specific API endpoints"""
        token = self.get_auth_token(self.customer_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        customer_endpoints = [
            '/api/v1/customer/dashboard/',
            '/api/v1/customer/profile/',
            '/api/v1/customer/bookings/',
            '/api/v1/customer/search/',
        ]
        
        for endpoint in customer_endpoints:
            with self.subTest(endpoint=endpoint):
                response = self.client.get(endpoint)
                # Should be accessible (not 404) and either work or require proper auth
                self.assertIn(response.status_code, [200, 401, 403, 405],
                            f"Customer endpoint {endpoint} should be accessible")
    
    def test_provider_api_endpoints(self):
        """Test provider-specific API endpoints"""
        token = self.get_auth_token(self.provider_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        provider_endpoints = [
            '/api/v1/provider/dashboard/',
            '/api/v1/provider/profile/',
            '/api/v1/provider/services/',
            '/api/v1/provider/bookings/',
        ]
        
        for endpoint in provider_endpoints:
            with self.subTest(endpoint=endpoint):
                response = self.client.get(endpoint)
                # Should be accessible (not 404) and either work or require proper auth
                self.assertIn(response.status_code, [200, 401, 403, 405],
                            f"Provider endpoint {endpoint} should be accessible")
    
    def test_shared_api_endpoints(self):
        """Test shared API endpoints accessible to both roles"""
        token = self.get_auth_token(self.customer_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        shared_endpoints = [
            '/api/v1/shared/categories/',
            '/api/v1/shared/locations/',
            '/api/v1/shared/health/',
        ]
        
        for endpoint in shared_endpoints:
            with self.subTest(endpoint=endpoint):
                response = self.client.get(endpoint)
                # Should be accessible (not 404)
                self.assertNotEqual(response.status_code, 404,
                                  f"Shared endpoint {endpoint} should exist")
    
    def test_legacy_api_still_works(self):
        """Test that legacy API endpoints still work for backward compatibility"""
        legacy_endpoints = [
            '/api/auth/profile/',
            '/api/catalog/categories/',
            '/api/catalog/services/',
        ]
        
        token = self.get_auth_token(self.customer_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        for endpoint in legacy_endpoints:
            with self.subTest(endpoint=endpoint):
                response = self.client.get(endpoint)
                # Legacy endpoints should still work
                self.assertNotEqual(response.status_code, 404,
                                  f"Legacy endpoint {endpoint} should still work")
    
    def test_role_based_access_control(self):
        """Test that role-based access control works properly"""
        customer_token = self.get_auth_token(self.customer_user)
        provider_token = self.get_auth_token(self.provider_user)
        
        # Test customer accessing provider endpoints (should be restricted)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {customer_token}')
        response = self.client.get('/api/v1/provider/dashboard/')
        self.assertIn(response.status_code, [401, 403],
                     "Customer should not access provider endpoints")
        
        # Test provider accessing customer endpoints (should be restricted)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {provider_token}')
        response = self.client.get('/api/v1/customer/dashboard/')
        self.assertIn(response.status_code, [401, 403],
                     "Provider should not access customer endpoints")
    
    def test_api_versioning_structure(self):
        """Test that API versioning structure is properly implemented"""
        # Test that v1 is the current version
        response = self.client.get('/api/v1/')
        self.assertNotEqual(response.status_code, 404,
                          "API v1 should be accessible")
        
        # Test that the structure follows the expected pattern
        # /api/v1/{role}/{resource}/
        test_patterns = [
            ('/api/v1/customer/profile/', 'customer', 'profile'),
            ('/api/v1/provider/services/', 'provider', 'services'),
            ('/api/v1/shared/categories/', 'shared', 'categories'),
        ]
        
        for url, expected_role, expected_resource in test_patterns:
            with self.subTest(url=url):
                # Test URL resolution
                try:
                    resolved = resolve(url)
                    # URL should resolve without error
                    self.assertIsNotNone(resolved)
                except Exception:
                    self.fail(f"URL {url} should resolve properly")
    
    def test_api_documentation_updated(self):
        """Test that API documentation reflects the new structure"""
        # Test that API docs are accessible
        response = self.client.get('/api/docs/')
        self.assertIn(response.status_code, [200, 302],
                     "API documentation should be accessible")
        
        # Test schema endpoint
        response = self.client.get('/api/schema/')
        self.assertIn(response.status_code, [200, 302],
                     "API schema should be accessible")


if __name__ == '__main__':
    import unittest
    unittest.main()
