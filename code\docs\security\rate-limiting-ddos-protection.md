# Advanced Rate Limiting and DDoS Protection Documentation

## Overview

This document describes the comprehensive rate limiting and DDoS protection system implemented to protect the application from abuse, attacks, and ensure system availability.

**Implementation Date:** August 8, 2025  
**Epic:** EPIC-AUDIT-002 - Production Security Implementation  
**Status:** ✅ COMPLETED  
**Components:** Redis-based rate limiting, IP blocking, DDoS protection, middleware integration

## Architecture

### Core Components

1. **Redis Rate Limiter** (`vierla_project/utils/rate_limiting.py`)
   - Sliding window algorithm implementation
   - Burst protection capabilities
   - Redis-based storage for performance
   - Fallback to Django cache

2. **IP Block Manager**
   - Automatic IP blocking and unblocking
   - Whitelist and blacklist management
   - Suspicious IP tracking
   - Configurable block durations

3. **DDoS Protector**
   - Request pattern analysis
   - Threat level assessment
   - Automatic protection activation
   - Attack signature detection

4. **Advanced Rate Limiting Middleware**
   - Multi-tier rate limiting
   - Endpoint-specific limits
   - User-tier based limiting
   - Security headers integration

## Key Features

### Multi-Tier Rate Limiting

The system implements different rate limits based on user types:

```python
default_limits = {
    'anonymous': {'limit': 100, 'window': 3600, 'burst': 20},
    'authenticated': {'limit': 1000, 'window': 3600, 'burst': 50},
    'premium': {'limit': 5000, 'window': 3600, 'burst': 100},
}
```

### Endpoint-Specific Limits

Critical endpoints have specialized rate limits:

```python
endpoint_limits = {
    'auth_login': {'limit': 10, 'window': 600, 'burst': 5},
    'auth_register': {'limit': 5, 'window': 3600, 'burst': 2},
    'password_reset': {'limit': 3, 'window': 3600, 'burst': 1},
    'search': {'limit': 200, 'window': 3600, 'burst': 30},
    'upload': {'limit': 50, 'window': 3600, 'burst': 10},
}
```

### Sliding Window Algorithm

```python
def _redis_sliding_window(self, key, limit, window, now, burst_limit=None):
    """Redis-based sliding window rate limiting."""
    
    pipe = self.redis_client.pipeline()
    
    # Remove old entries
    pipe.zremrangebyscore(key, 0, now - window)
    
    # Count current requests
    pipe.zcard(key)
    
    # Add current request
    pipe.zadd(key, {str(now): now})
    
    # Set expiration
    pipe.expire(key, window + 1)
    
    results = pipe.execute()
    current_count = results[1] + 1
    
    allowed = current_count <= limit
    return allowed, info_dict
```

## DDoS Protection

### Threat Detection

The system analyzes multiple factors to detect threats:

```python
ddos_thresholds = {
    'requests_per_second': 50,
    'requests_per_minute': 1000,
    'failed_requests_ratio': 0.8,
    'unique_endpoints_threshold': 20,
    'suspicious_user_agents': ['bot', 'crawler', 'spider', 'scraper']
}
```

### Pattern Analysis

```python
def analyze_request_pattern(self, ip, request_info):
    """Analyze request patterns for DDoS detection."""
    
    analysis = {
        'ip': ip,
        'threat_level': 'low',
        'reasons': [],
        'action': 'allow',
        'block_duration': 0
    }
    
    # Check request rate
    rate_analysis = self._analyze_request_rate(ip)
    
    # Check request patterns
    pattern_analysis = self._analyze_request_patterns(ip, request_info)
    
    # Determine action based on threat level
    if analysis['threat_level'] == 'critical':
        analysis['action'] = 'block'
        analysis['block_duration'] = 3600
    
    return analysis
```

### Automatic IP Blocking

```python
def block_ip(self, ip, reason="DDoS protection", duration=3600):
    """Block an IP address with reason tracking."""
    
    # Add to blocked set
    self.redis_client.sadd(self.blocked_ips_key, ip)
    
    # Store block information
    block_info = {
        'ip': ip,
        'reason': reason,
        'blocked_at': time.time(),
        'duration': duration,
        'expires_at': time.time() + duration
    }
    
    block_key = f"security:block_info:{ip}"
    self.redis_client.setex(block_key, duration, json.dumps(block_info))
```

## Middleware Integration

### Advanced Rate Limiting Middleware

```python
class AdvancedRateLimitingMiddleware(MiddlewareMixin):
    """Advanced rate limiting with DDoS protection."""
    
    def process_request(self, request):
        client_ip = self._get_client_ip(request)
        user = getattr(request, 'user', AnonymousUser())
        
        # Skip whitelisted IPs
        if ip_manager.is_ip_whitelisted(client_ip):
            return None
        
        # Check blocked IPs
        if ip_manager.is_ip_blocked(client_ip):
            return self._create_blocked_response(client_ip)
        
        # DDoS protection analysis
        if self.ddos_enabled:
            request_info = self._extract_request_info(request)
            ddos_analysis = ddos_protector.analyze_request_pattern(client_ip, request_info)
            
            if ddos_analysis['action'] == 'block':
                if self.auto_block_enabled:
                    ip_manager.block_ip(client_ip, reason, duration)
                return self._create_ddos_response(client_ip, ddos_analysis)
        
        # Apply rate limiting
        rate_limit_result = self._apply_rate_limiting(request, client_ip, user)
        
        if not rate_limit_result['allowed']:
            return self._create_rate_limit_response(rate_limit_result)
        
        return None
```

### Authentication Rate Limiting

```python
class AuthenticationRateLimitingMiddleware(MiddlewareMixin):
    """Specialized authentication endpoint rate limiting."""
    
    def process_request(self, request):
        if not self._is_auth_endpoint(request):
            return None
        
        client_ip = self._get_client_ip(request)
        
        # Check authentication blocks
        if self._is_auth_blocked(client_ip):
            return self._create_auth_blocked_response()
        
        # Apply authentication rate limiting
        auth_type = self._get_auth_type(request)
        if auth_type:
            limits = self.auth_limits.get(auth_type)
            
            allowed, info = rate_limiter.is_allowed(
                f"auth:{auth_type}:{client_ip}",
                limits['limit'],
                limits['window']
            )
            
            if not allowed:
                ip_manager.block_ip(client_ip, "Auth rate limit", limits['block_duration'])
                return self._create_auth_rate_limit_response(auth_type, info)
        
        return None
```

## Rate Limiting Decorators

### Basic Rate Limiting

```python
@rate_limit(limit=100, window=3600, burst_limit=20)
def my_view(request):
    """View with rate limiting applied."""
    return Response({'message': 'Success'})
```

### Authentication Rate Limiting

```python
@authentication_rate_limit(
    login_limit=5,
    login_window=900,
    failed_limit=3,
    failed_window=300,
    block_duration=1800
)
def login_view(request):
    """Login view with specialized rate limiting."""
    return authenticate_user(request)
```

### DDoS Protection

```python
@ddos_protection(
    enable_pattern_analysis=True,
    enable_auto_block=True,
    suspicious_threshold=50
)
def high_risk_endpoint(request):
    """High-risk endpoint with DDoS protection."""
    return process_request(request)
```

### Burst Protection

```python
@burst_protection(
    burst_limit=20,
    burst_window=60,
    sustained_limit=100,
    sustained_window=3600
)
def api_endpoint(request):
    """API endpoint with burst protection."""
    return api_response(request)
```

## Configuration

### Redis Configuration

```python
REDIS_CONFIG = {
    'host': os.getenv('REDIS_HOST', 'localhost'),
    'port': int(os.getenv('REDIS_PORT', 6379)),
    'db': int(os.getenv('REDIS_DB', 1)),
    'decode_responses': True,
    'socket_connect_timeout': 5,
    'socket_timeout': 5,
    'retry_on_timeout': True,
    'health_check_interval': 30,
}
```

### Rate Limiting Configuration

```python
ADVANCED_RATE_LIMITING = {
    'ddos_protection': True,
    'auto_block': True,
    'default_limits': {
        'anonymous': {'limit': 100, 'window': 3600, 'burst': 20},
        'authenticated': {'limit': 1000, 'window': 3600, 'burst': 50},
        'premium': {'limit': 5000, 'window': 3600, 'burst': 100},
    },
    'ddos_thresholds': {
        'requests_per_second': 50,
        'requests_per_minute': 1000,
        'failed_requests_ratio': 0.8,
        'unique_endpoints_threshold': 20,
    },
    'block_durations': {
        'low_threat': 300,
        'medium_threat': 1800,
        'high_threat': 3600,
        'critical_threat': 7200,
    }
}
```

### Production Configuration

Production environments use more restrictive limits:

```python
# Production overrides
ADVANCED_RATE_LIMITING.update({
    'default_limits': {
        'anonymous': {'limit': 50, 'window': 3600, 'burst': 10},
        'authenticated': {'limit': 500, 'window': 3600, 'burst': 25},
        'premium': {'limit': 2000, 'window': 3600, 'burst': 50},
    },
    'ddos_thresholds': {
        'requests_per_second': 20,
        'requests_per_minute': 500,
        'failed_requests_ratio': 0.7,
        'unique_endpoints_threshold': 15,
    }
})
```

## Management Commands

### IP Management

```bash
# Block an IP
python manage.py manage_rate_limits --block-ip ************* --reason "Manual block" --duration 3600

# Unblock an IP
python manage.py manage_rate_limits --unblock-ip *************

# Whitelist an IP
python manage.py manage_rate_limits --whitelist-ip ***********
```

### Monitoring

```bash
# List blocked IPs
python manage.py manage_rate_limits --list-blocked

# Show statistics
python manage.py manage_rate_limits --stats

# List suspicious IPs
python manage.py manage_rate_limits --list-suspicious
```

### Maintenance

```bash
# Clean up expired entries
python manage.py manage_rate_limits --cleanup-expired

# Reset rate limits
python manage.py manage_rate_limits --reset-limits all

# Test DDoS protection
python manage.py manage_rate_limits --test-ddos *************
```

## Security Headers

The middleware automatically adds security headers:

```python
security_headers = {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'X-Rate-Limit-Policy': 'advanced'
}
```

### Rate Limiting Headers

```python
response['X-RateLimit-Limit'] = str(limit)
response['X-RateLimit-Remaining'] = str(remaining)
response['X-RateLimit-Reset'] = str(reset_time)
response['X-RateLimit-Window'] = str(window)
response['X-RateLimit-Tier'] = user_tier
```

## Monitoring and Alerting

### Metrics Tracking

- Request rates per IP and user
- Block events and reasons
- DDoS attack attempts
- Rate limit violations
- System performance impact

### Alert Conditions

- High-volume attacks detected
- Critical IPs blocked
- System performance degradation
- Redis connectivity issues
- Configuration errors

## Performance Considerations

### Redis Optimization

- Connection pooling for performance
- Pipeline operations for efficiency
- Appropriate key expiration
- Memory usage monitoring

### Middleware Ordering

```python
MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    # Rate limiting before authentication
    'vierla_project.middleware.advanced_rate_limiting.AdvancedRateLimitingMiddleware',
    'vierla_project.middleware.advanced_rate_limiting.AuthenticationRateLimitingMiddleware',
    # ... other middleware
]
```

## Conclusion

The Advanced Rate Limiting and DDoS Protection system provides:

- ✅ **Multi-Tier Rate Limiting**: User and endpoint-specific limits
- ✅ **DDoS Protection**: Automatic threat detection and blocking
- ✅ **IP Management**: Comprehensive blocking and whitelisting
- ✅ **Redis Integration**: High-performance storage backend
- ✅ **Middleware Integration**: Seamless Django integration
- ✅ **Management Tools**: CLI commands for administration
- ✅ **Security Headers**: Comprehensive security headers
- ✅ **Production Ready**: Scalable and configurable

This system provides enterprise-grade protection against abuse and attacks while maintaining system performance and availability.
