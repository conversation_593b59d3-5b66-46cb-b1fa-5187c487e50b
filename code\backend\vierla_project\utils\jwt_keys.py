"""
JWT RSA Key Management Utilities

This module provides utilities for loading and managing RSA keys for JWT RS256 algorithm.
Part of EPIC-AUDIT-002 - Production Security Implementation.

Features:
- Load RSA keys from environment variables (Base64 or PEM format)
- Validate key pairs for JWT signing/verification
- Secure key handling with error management
- Support for both development and production environments
"""

import os
import base64
import logging
from typing import Tuple, Optional
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography.hazmat.backends import default_backend

logger = logging.getLogger(__name__)

class JWTKeyError(Exception):
    """Custom exception for JWT key-related errors."""
    pass

class JWTKeyManager:
    """
    Manages RSA key pairs for JWT RS256 algorithm.
    """
    
    def __init__(self):
        self._private_key = None
        self._public_key = None
        self._keys_loaded = False
    
    def load_keys_from_env(self) -> <PERSON>ple[str, str]:
        """
        Load RSA key pair from environment variables.
        
        Supports both Base64 encoded and direct PEM format keys.
        
        Returns:
            Tuple[str, str]: (private_key_pem, public_key_pem)
            
        Raises:
            JWTKeyError: If keys cannot be loaded or are invalid
        """
        try:
            # Try Base64 encoded keys first (recommended for environment variables)
            private_key_b64 = os.environ.get('JWT_PRIVATE_KEY_B64')
            public_key_b64 = os.environ.get('JWT_PUBLIC_KEY_B64')
            
            if private_key_b64 and public_key_b64:
                logger.info("Loading JWT keys from Base64 environment variables")
                
                # Decode Base64 keys
                private_key_pem = base64.b64decode(private_key_b64).decode('utf-8')
                public_key_pem = base64.b64decode(public_key_b64).decode('utf-8')
                
                # Validate the keys
                self._validate_key_format(private_key_pem, public_key_pem)
                
                self._private_key = private_key_pem
                self._public_key = public_key_pem
                self._keys_loaded = True
                
                logger.info("✅ JWT RSA keys loaded successfully from Base64 environment variables")
                return private_key_pem, public_key_pem
            
            # Fallback to direct PEM format keys
            private_key_pem = os.environ.get('JWT_PRIVATE_KEY')
            public_key_pem = os.environ.get('JWT_PUBLIC_KEY')
            
            if private_key_pem and public_key_pem:
                logger.info("Loading JWT keys from PEM environment variables")
                
                # Handle escaped newlines in environment variables
                private_key_pem = private_key_pem.replace('\\n', '\n')
                public_key_pem = public_key_pem.replace('\\n', '\n')
                
                # Validate the keys
                self._validate_key_format(private_key_pem, public_key_pem)
                
                self._private_key = private_key_pem
                self._public_key = public_key_pem
                self._keys_loaded = True
                
                logger.info("✅ JWT RSA keys loaded successfully from PEM environment variables")
                return private_key_pem, public_key_pem
            
            # No keys found in environment
            raise JWTKeyError(
                "JWT RSA keys not found in environment variables. "
                "Please set JWT_PRIVATE_KEY_B64 and JWT_PUBLIC_KEY_B64 or "
                "JWT_PRIVATE_KEY and JWT_PUBLIC_KEY environment variables."
            )
            
        except base64.binascii.Error as e:
            raise JWTKeyError(f"Invalid Base64 encoding in JWT keys: {e}")
        except Exception as e:
            raise JWTKeyError(f"Error loading JWT keys from environment: {e}")
    
    def _validate_key_format(self, private_key_pem: str, public_key_pem: str) -> None:
        """
        Validate that the provided keys are valid RSA keys.
        
        Args:
            private_key_pem (str): PEM-encoded private key
            public_key_pem (str): PEM-encoded public key
            
        Raises:
            JWTKeyError: If keys are invalid or don't match
        """
        try:
            # Load private key
            private_key = serialization.load_pem_private_key(
                private_key_pem.encode('utf-8'),
                password=None,
                backend=default_backend()
            )
            
            # Verify it's an RSA key
            if not isinstance(private_key, rsa.RSAPrivateKey):
                raise JWTKeyError("Private key is not an RSA key")
            
            # Load public key
            public_key = serialization.load_pem_public_key(
                public_key_pem.encode('utf-8'),
                backend=default_backend()
            )
            
            # Verify it's an RSA key
            if not isinstance(public_key, rsa.RSAPublicKey):
                raise JWTKeyError("Public key is not an RSA key")
            
            # Verify keys match (public key from private key should match provided public key)
            derived_public_key = private_key.public_key()
            
            # Compare key parameters
            if (derived_public_key.key_size != public_key.key_size or
                derived_public_key.public_numbers().n != public_key.public_numbers().n or
                derived_public_key.public_numbers().e != public_key.public_numbers().e):
                raise JWTKeyError("Private and public keys do not match")
            
            logger.info(f"✅ JWT RSA key pair validated (key size: {private_key.key_size} bits)")
            
        except ValueError as e:
            raise JWTKeyError(f"Invalid key format: {e}")
        except Exception as e:
            raise JWTKeyError(f"Key validation failed: {e}")
    
    def get_private_key(self) -> str:
        """
        Get the private key for JWT signing.
        
        Returns:
            str: PEM-encoded private key
            
        Raises:
            JWTKeyError: If keys are not loaded
        """
        if not self._keys_loaded:
            self.load_keys_from_env()
        
        return self._private_key
    
    def get_public_key(self) -> str:
        """
        Get the public key for JWT verification.
        
        Returns:
            str: PEM-encoded public key
            
        Raises:
            JWTKeyError: If keys are not loaded
        """
        if not self._keys_loaded:
            self.load_keys_from_env()
        
        return self._public_key
    
    def get_key_info(self) -> dict:
        """
        Get information about the loaded keys.
        
        Returns:
            dict: Key information including algorithm, key size, etc.
        """
        if not self._keys_loaded:
            self.load_keys_from_env()
        
        try:
            private_key = serialization.load_pem_private_key(
                self._private_key.encode('utf-8'),
                password=None,
                backend=default_backend()
            )
            
            return {
                'algorithm': 'RS256',
                'key_type': 'RSA',
                'key_size': private_key.key_size,
                'public_exponent': private_key.public_key().public_numbers().e,
                'keys_loaded': self._keys_loaded,
                'source': 'environment_variables'
            }
            
        except Exception as e:
            logger.error(f"Error getting key info: {e}")
            return {
                'algorithm': 'RS256',
                'key_type': 'RSA',
                'keys_loaded': self._keys_loaded,
                'error': str(e)
            }

# Global instance for use in Django settings
jwt_key_manager = JWTKeyManager()

def get_jwt_private_key() -> str:
    """
    Get JWT private key for Django settings.
    
    Returns:
        str: PEM-encoded private key
    """
    return jwt_key_manager.get_private_key()

def get_jwt_public_key() -> str:
    """
    Get JWT public key for Django settings.
    
    Returns:
        str: PEM-encoded public key
    """
    return jwt_key_manager.get_public_key()

def validate_jwt_configuration() -> dict:
    """
    Validate JWT configuration and return status.
    
    Returns:
        dict: Validation status and information
    """
    try:
        # Try to load keys
        private_key, public_key = jwt_key_manager.load_keys_from_env()
        key_info = jwt_key_manager.get_key_info()
        
        return {
            'status': 'success',
            'message': 'JWT RSA keys loaded and validated successfully',
            'key_info': key_info,
            'private_key_length': len(private_key),
            'public_key_length': len(public_key)
        }
        
    except JWTKeyError as e:
        return {
            'status': 'error',
            'message': str(e),
            'key_info': None
        }
    except Exception as e:
        return {
            'status': 'error',
            'message': f'Unexpected error: {e}',
            'key_info': None
        }

# Development fallback for when keys are not available
def get_development_fallback_key() -> str:
    """
    Get a fallback key for development when RSA keys are not configured.
    
    Returns:
        str: Django SECRET_KEY for HS256 fallback
    """
    from django.conf import settings
    logger.warning(
        "⚠️ JWT RSA keys not found, falling back to HS256 with Django SECRET_KEY. "
        "This is NOT secure for production!"
    )
    return settings.SECRET_KEY
