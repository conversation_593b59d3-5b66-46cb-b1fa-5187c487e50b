/**
 * Edit Profile Screen
 * Shared screen for editing user profile (both customer and provider)
 */

import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export const EditProfileScreen: React.FC = () => {
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content}>
        <Text style={styles.title}>Edit Profile</Text>
        <Text style={styles.subtitle}>Update your profile information</Text>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Profile Information</Text>
          <Text style={styles.text}>
            This is a placeholder for the edit profile screen. 
            This screen will allow users to update their profile information.
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Features</Text>
          <Text style={styles.text}>• Update personal information</Text>
          <Text style={styles.text}>• Change profile picture</Text>
          <Text style={styles.text}>• Update contact details</Text>
          <Text style={styles.text}>• Manage privacy settings</Text>
          <Text style={styles.text}>• Update location information</Text>
          <Text style={styles.text}>• Save and sync changes</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Profile Fields</Text>
          <View style={styles.fieldContainer}>
            <Text style={styles.fieldLabel}>Full Name</Text>
            <View style={styles.fieldPlaceholder}>
              <Text style={styles.fieldText}>Enter your full name</Text>
            </View>
          </View>
          
          <View style={styles.fieldContainer}>
            <Text style={styles.fieldLabel}>Email</Text>
            <View style={styles.fieldPlaceholder}>
              <Text style={styles.fieldText}>Enter your email address</Text>
            </View>
          </View>
          
          <View style={styles.fieldContainer}>
            <Text style={styles.fieldLabel}>Phone</Text>
            <View style={styles.fieldPlaceholder}>
              <Text style={styles.fieldText}>Enter your phone number</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    marginBottom: 24,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 12,
  },
  text: {
    fontSize: 16,
    color: '#333333',
    lineHeight: 24,
    marginBottom: 8,
  },
  fieldContainer: {
    marginBottom: 16,
  },
  fieldLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 8,
  },
  fieldPlaceholder: {
    backgroundColor: '#F8F9FA',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  fieldText: {
    fontSize: 16,
    color: '#999999',
  },
});

export default EditProfileScreen;
