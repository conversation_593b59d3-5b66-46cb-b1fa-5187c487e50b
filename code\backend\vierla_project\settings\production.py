"""
<PERSON><PERSON><PERSON> Backend - Production Settings
"""
from .base import *

# Production security settings
DEBUG = False
ALLOWED_HOSTS = os.environ.get('ALLOWED_HOSTS', '').split(',')

# Security middleware
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
] + MIDDLEWARE

# Database configuration with connection pooling for production
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.environ.get('DB_NAME'),
        'USER': os.environ.get('DB_USER'),
        'PASSWORD': os.environ.get('DB_PASSWORD'),
        'HOST': os.environ.get('DB_HOST', 'localhost'),
        'PORT': os.environ.get('DB_PORT', '5432'),
        'OPTIONS': {
            'sslmode': 'require',
            'connect_timeout': 10,
            'options': '-c default_transaction_isolation=serializable'
        },
        # Production connection pooling and performance settings
        'CONN_MAX_AGE': 600,  # 10 minutes
        'CONN_HEALTH_CHECKS': True,
        'ATOMIC_REQUESTS': False,  # Disabled for better performance
    }
}

# Production security settings
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SECURE_HSTS_SECONDS = 31536000  # 1 year
SECURE_REFERRER_POLICY = 'strict-origin-when-cross-origin'
SECURE_SSL_REDIRECT = True
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
X_FRAME_OPTIONS = 'DENY'

# Production JWT settings with stronger security
SIMPLE_JWT.update({
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=15),  # Shorter for production
    'REFRESH_TOKEN_LIFETIME': timedelta(days=1),     # Shorter for production
    'ALGORITHM': 'RS256',  # Use asymmetric encryption in production
    'SIGNING_KEY': os.environ.get('JWT_PRIVATE_KEY'),
    'VERIFYING_KEY': os.environ.get('JWT_PUBLIC_KEY'),
})

# Production throttle rates
REST_FRAMEWORK['DEFAULT_THROTTLE_RATES'] = {
    'anon': '100/hour',
    'user': '1000/hour',
    'mobile': '500/hour',
    'login': '10/minute',
    'register': '5/minute',
    'password_reset': '3/hour',
}

# Production CORS settings
CORS_ALLOW_ALL_ORIGINS = False
CORS_ALLOWED_ORIGINS = os.environ.get('CORS_ALLOWED_ORIGINS', '').split(',')

# Production email settings
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = os.environ.get('EMAIL_HOST')
EMAIL_PORT = int(os.environ.get('EMAIL_PORT', 587))
EMAIL_USE_TLS = True
EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER')
EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD')

# Production caching with Redis
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': os.environ.get('REDIS_URL', 'redis://localhost:6379/0'),
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    },
    'sessions': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': os.environ.get('REDIS_URL', 'redis://localhost:6379/1'),
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}

# Use Redis for sessions in production
SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_CACHE_ALIAS = 'sessions'

# Production logging
LOGGING['handlers']['file']['filename'] = '/var/log/vierla/django.log'
LOGGING['handlers']['console']['level'] = 'WARNING'
LOGGING['loggers']['django']['level'] = 'WARNING'
LOGGING['loggers']['apps']['level'] = 'INFO'

# Static files handling with WhiteNoise
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

# Celery configuration for production
CELERY_BROKER_URL = os.environ.get('REDIS_URL', 'redis://localhost:6379/0')
CELERY_RESULT_BACKEND = os.environ.get('REDIS_URL', 'redis://localhost:6379/0')
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = TIME_ZONE

# Production Rate Limiting Configuration (EPIC-AUDIT-002)
# Override base settings with more restrictive production limits
ADVANCED_RATE_LIMITING.update({
    'default_limits': {
        'anonymous': {'limit': 50, 'window': 3600, 'burst': 10},    # 50/hour, 10/minute burst
        'authenticated': {'limit': 500, 'window': 3600, 'burst': 25},  # 500/hour, 25/minute burst
        'premium': {'limit': 2000, 'window': 3600, 'burst': 50},   # 2000/hour, 50/minute burst
    },
    'endpoint_limits': {
        'auth_login': {'limit': 5, 'window': 600, 'burst': 3},     # 5/10min, 3/minute burst
        'auth_register': {'limit': 3, 'window': 3600, 'burst': 1}, # 3/hour, 1/minute burst
        'password_reset': {'limit': 2, 'window': 3600, 'burst': 1}, # 2/hour, 1/minute burst
        'search': {'limit': 100, 'window': 3600, 'burst': 15},     # 100/hour, 15/minute burst
        'upload': {'limit': 20, 'window': 3600, 'burst': 5},       # 20/hour, 5/minute burst
    },
    'ddos_thresholds': {
        'requests_per_second': 20,      # More restrictive in production
        'requests_per_minute': 500,     # More restrictive in production
        'failed_requests_ratio': 0.7,   # More sensitive to failed requests
        'unique_endpoints_threshold': 15, # More sensitive to scanning
        'suspicious_user_agents': [
            'bot', 'crawler', 'spider', 'scraper', 'scanner', 'wget', 'curl',
            'python-requests', 'go-http-client', 'java/', 'apache-httpclient'
        ],
    }
})

# Production Redis Configuration for Rate Limiting
REDIS_CONFIG.update({
    'host': os.environ.get('REDIS_HOST', 'redis'),
    'port': int(os.environ.get('REDIS_PORT', 6379)),
    'password': os.environ.get('REDIS_PASSWORD'),
    'ssl': os.environ.get('REDIS_SSL', 'false').lower() == 'true',
    'ssl_cert_reqs': None if os.environ.get('REDIS_SSL', 'false').lower() == 'true' else None,
})

# Production Security Headers
SECURE_SSL_REDIRECT = True
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
X_FRAME_OPTIONS = 'DENY'
SECURE_REFERRER_POLICY = 'strict-origin-when-cross-origin'

# Session security
SESSION_COOKIE_SECURE = True
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_SAMESITE = 'Strict'
CSRF_COOKIE_SECURE = True
CSRF_COOKIE_HTTPONLY = True
CSRF_COOKIE_SAMESITE = 'Strict'
