"""
Test suite for PostgreSQL connection settings configuration.

This test file is part of EPIC-AUDIT-001 - Database Configuration Overhaul
Task: Configure PostgreSQL Connection Settings
"""

import os
import sys
import unittest
from unittest.mock import patch, MagicMock
import psycopg2
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

class PostgreSQLConnectionSettingsTest(unittest.TestCase):
    """
    Test PostgreSQL connection settings and optimization configuration.
    """
    
    def setUp(self):
        """Set up test environment"""
        self.backend_dir = Path(__file__).parent
        self.original_env = dict(os.environ)
        
    def tearDown(self):
        """Clean up test environment"""
        # Restore original environment
        os.environ.clear()
        os.environ.update(self.original_env)
    
    def test_postgresql_connection_parameters(self):
        """
        Test that PostgreSQL connection uses correct parameters from environment
        """
        # Set test environment variables
        test_env = {
            'DB_NAME': 'test_vierla_db',
            'DB_USER': 'test_vierla_user',
            'DB_PASSWORD': 'test_vierla_password',
            'DB_HOST': 'test_localhost',
            'DB_PORT': '5433',
            'DB_SSLMODE': 'require'
        }
        
        with patch.dict(os.environ, test_env):
            # Clear module cache to force reimport
            if 'vierla_project.settings.development' in sys.modules:
                del sys.modules['vierla_project.settings.development']
            
            from vierla_project.settings import development
            
            # Test that PostgreSQL configuration uses environment variables
            if hasattr(development, 'DATABASES') and development.DATABASES['default']['ENGINE'] == 'django.db.backends.postgresql':
                db_config = development.DATABASES['default']
                
                self.assertEqual(db_config['NAME'], 'test_vierla_db')
                self.assertEqual(db_config['USER'], 'test_vierla_user')
                self.assertEqual(db_config['PASSWORD'], 'test_vierla_password')
                self.assertEqual(db_config['HOST'], 'test_localhost')
                self.assertEqual(db_config['PORT'], '5433')
                
                # Test SSL configuration
                if 'OPTIONS' in db_config:
                    self.assertEqual(db_config['OPTIONS']['sslmode'], 'require')
                
                print("✅ PostgreSQL connection parameters configured correctly")
            else:
                print("ℹ️ System is using SQLite fallback - PostgreSQL parameters not active")
    
    def test_postgresql_optimization_settings(self):
        """
        Test that PostgreSQL optimization settings are properly configured
        """
        # Mock successful PostgreSQL connection to test optimization settings
        with patch('psycopg2.connect') as mock_connect:
            mock_conn = MagicMock()
            mock_connect.return_value = mock_conn
            
            # Set environment for PostgreSQL
            test_env = {
                'USE_SQLITE': 'false',
                'DB_NAME': 'vierla_db',
                'DB_USER': 'vierla_user',
                'DB_PASSWORD': 'vierla_password',
                'DB_HOST': 'localhost',
                'DB_PORT': '5432'
            }
            
            with patch.dict(os.environ, test_env):
                # Clear module cache to force reimport
                if 'vierla_project.settings.development' in sys.modules:
                    del sys.modules['vierla_project.settings.development']
                
                from vierla_project.settings import development
                
                # Verify optimization settings are present
                if hasattr(development, 'DATABASES'):
                    db_config = development.DATABASES['default']
                    
                    if db_config['ENGINE'] == 'django.db.backends.postgresql':
                        # Test connection pooling settings
                        self.assertEqual(db_config.get('CONN_MAX_AGE'), 600)
                        self.assertEqual(db_config.get('CONN_HEALTH_CHECKS'), True)
                        self.assertEqual(db_config.get('ATOMIC_REQUESTS'), False)
                        
                        # Test connection options
                        options = db_config.get('OPTIONS', {})
                        self.assertIn('connect_timeout', options)
                        self.assertEqual(options['connect_timeout'], 10)
                        self.assertIn('options', options)
                        self.assertIn('default_transaction_isolation=read_committed', options['options'])
                        
                        print("✅ PostgreSQL optimization settings configured correctly")
                        print(f"   CONN_MAX_AGE: {db_config.get('CONN_MAX_AGE')}")
                        print(f"   CONN_HEALTH_CHECKS: {db_config.get('CONN_HEALTH_CHECKS')}")
                        print(f"   Connect Timeout: {options.get('connect_timeout')}")
                        print(f"   SSL Mode: {options.get('sslmode', 'prefer')}")
                    else:
                        print("ℹ️ System is using SQLite fallback - optimization settings not active")
    
    def test_ssl_configuration_options(self):
        """
        Test SSL configuration options for PostgreSQL connections
        """
        ssl_modes = ['disable', 'allow', 'prefer', 'require', 'verify-ca', 'verify-full']
        
        for ssl_mode in ssl_modes:
            with patch('psycopg2.connect') as mock_connect:
                mock_conn = MagicMock()
                mock_connect.return_value = mock_conn
                
                test_env = {
                    'USE_SQLITE': 'false',
                    'DB_SSLMODE': ssl_mode,
                    'DB_NAME': 'vierla_db',
                    'DB_USER': 'vierla_user',
                    'DB_PASSWORD': 'vierla_password',
                    'DB_HOST': 'localhost',
                    'DB_PORT': '5432'
                }
                
                with patch.dict(os.environ, test_env):
                    # Clear module cache to force reimport
                    if 'vierla_project.settings.development' in sys.modules:
                        del sys.modules['vierla_project.settings.development']
                    
                    from vierla_project.settings import development
                    
                    if hasattr(development, 'DATABASES'):
                        db_config = development.DATABASES['default']
                        
                        if db_config['ENGINE'] == 'django.db.backends.postgresql':
                            options = db_config.get('OPTIONS', {})
                            self.assertEqual(options.get('sslmode'), ssl_mode)
                            print(f"✅ SSL mode '{ssl_mode}' configured correctly")
    
    def test_connection_timeout_configuration(self):
        """
        Test connection timeout configuration
        """
        timeout_values = [5, 10, 15, 30]
        
        for timeout in timeout_values:
            with patch('psycopg2.connect') as mock_connect:
                mock_conn = MagicMock()
                mock_connect.return_value = mock_conn
                
                # Test that test_postgresql_connection uses the timeout
                test_env = {
                    'DB_NAME': 'vierla_db',
                    'DB_USER': 'vierla_user',
                    'DB_PASSWORD': 'vierla_password',
                    'DB_HOST': 'localhost',
                    'DB_PORT': '5432'
                }
                
                with patch.dict(os.environ, test_env):
                    from vierla_project.settings.development import test_postgresql_connection
                    
                    result = test_postgresql_connection()
                    self.assertTrue(result)
                    
                    # Verify connection was called with timeout
                    mock_connect.assert_called()
                    call_kwargs = mock_connect.call_args[1]
                    self.assertEqual(call_kwargs['connect_timeout'], 5)  # Default timeout in function
                    
                    print(f"✅ Connection timeout configured correctly")
    
    def test_connection_pooling_settings(self):
        """
        Test connection pooling and performance settings
        """
        with patch('psycopg2.connect') as mock_connect:
            mock_conn = MagicMock()
            mock_connect.return_value = mock_conn
            
            test_env = {
                'USE_SQLITE': 'false',
                'DB_NAME': 'vierla_db',
                'DB_USER': 'vierla_user',
                'DB_PASSWORD': 'vierla_password',
                'DB_HOST': 'localhost',
                'DB_PORT': '5432'
            }
            
            with patch.dict(os.environ, test_env):
                # Clear module cache to force reimport
                if 'vierla_project.settings.development' in sys.modules:
                    del sys.modules['vierla_project.settings.development']
                
                from vierla_project.settings import development
                
                if hasattr(development, 'DATABASES'):
                    db_config = development.DATABASES['default']
                    
                    if db_config['ENGINE'] == 'django.db.backends.postgresql':
                        # Test connection pooling settings
                        self.assertIsNotNone(db_config.get('CONN_MAX_AGE'))
                        self.assertGreater(db_config.get('CONN_MAX_AGE', 0), 0)
                        
                        # Test health checks
                        self.assertTrue(db_config.get('CONN_HEALTH_CHECKS', False))
                        
                        # Test atomic requests setting (should be False for performance)
                        self.assertFalse(db_config.get('ATOMIC_REQUESTS', True))
                        
                        print("✅ Connection pooling settings configured correctly")
                        print(f"   Max Age: {db_config.get('CONN_MAX_AGE')} seconds")
                        print(f"   Health Checks: {db_config.get('CONN_HEALTH_CHECKS')}")
                        print(f"   Atomic Requests: {db_config.get('ATOMIC_REQUESTS')}")
    
    def test_transaction_isolation_level(self):
        """
        Test transaction isolation level configuration
        """
        with patch('psycopg2.connect') as mock_connect:
            mock_conn = MagicMock()
            mock_connect.return_value = mock_conn
            
            test_env = {
                'USE_SQLITE': 'false',
                'DB_NAME': 'vierla_db',
                'DB_USER': 'vierla_user',
                'DB_PASSWORD': 'vierla_password',
                'DB_HOST': 'localhost',
                'DB_PORT': '5432'
            }
            
            with patch.dict(os.environ, test_env):
                # Clear module cache to force reimport
                if 'vierla_project.settings.development' in sys.modules:
                    del sys.modules['vierla_project.settings.development']
                
                from vierla_project.settings import development
                
                if hasattr(development, 'DATABASES'):
                    db_config = development.DATABASES['default']
                    
                    if db_config['ENGINE'] == 'django.db.backends.postgresql':
                        options = db_config.get('OPTIONS', {})
                        postgres_options = options.get('options', '')
                        
                        self.assertIn('default_transaction_isolation=read_committed', postgres_options)
                        print("✅ Transaction isolation level configured correctly")
                        print(f"   Options: {postgres_options}")
    
    def test_fallback_behavior_with_connection_failure(self):
        """
        Test that system falls back to SQLite when PostgreSQL connection fails
        """
        with patch('psycopg2.connect', side_effect=psycopg2.OperationalError("Connection failed")):
            test_env = {
                'USE_SQLITE': 'false',  # Explicitly request PostgreSQL
                'DB_NAME': 'vierla_db',
                'DB_USER': 'vierla_user',
                'DB_PASSWORD': 'vierla_password',
                'DB_HOST': 'localhost',
                'DB_PORT': '5432'
            }
            
            with patch.dict(os.environ, test_env):
                # Clear module cache to force reimport
                if 'vierla_project.settings.development' in sys.modules:
                    del sys.modules['vierla_project.settings.development']
                
                from vierla_project.settings import development
                
                # Should fall back to SQLite
                db_config = development.DATABASES['default']
                self.assertEqual(db_config['ENGINE'], 'django.db.backends.sqlite3')
                
                print("✅ Fallback to SQLite works correctly when PostgreSQL fails")
    
    def test_environment_variable_defaults(self):
        """
        Test that appropriate defaults are used when environment variables are missing
        """
        # Remove database environment variables
        env_without_db = {k: v for k, v in os.environ.items() 
                         if not k.startswith('DB_')}
        
        with patch.dict(os.environ, env_without_db, clear=True):
            from vierla_project.settings.development import test_postgresql_connection
            
            # Should use default values
            with patch('psycopg2.connect') as mock_connect:
                mock_conn = MagicMock()
                mock_connect.return_value = mock_conn
                
                result = test_postgresql_connection()
                self.assertTrue(result)
                
                # Verify default values are used
                call_kwargs = mock_connect.call_args[1]
                self.assertEqual(call_kwargs['host'], 'localhost')
                self.assertEqual(call_kwargs['port'], '5432')
                self.assertEqual(call_kwargs['user'], 'vierla_user')
                self.assertEqual(call_kwargs['password'], 'vierla_password')
                self.assertEqual(call_kwargs['database'], 'vierla_db')
                
                print("✅ Default values used correctly when environment variables missing")


if __name__ == '__main__':
    # Run the PostgreSQL connection settings tests
    unittest.main(verbosity=2)
