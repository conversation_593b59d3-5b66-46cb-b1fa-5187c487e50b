"""
Enhanced Token Management System

This module provides comprehensive token rotation and blacklisting functionality
for JWT tokens with advanced security features.

Part of EPIC-AUDIT-002 - Production Security Implementation.

Features:
- Advanced token rotation with device tracking
- Comprehensive token blacklisting with reasons
- Token family management for security
- Suspicious activity detection
- Token analytics and monitoring
- Automatic cleanup of expired tokens
"""

import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.db import transaction
from rest_framework_simplejwt.tokens import RefreshToken, AccessToken
from rest_framework_simplejwt.token_blacklist.models import OutstandingToken, BlacklistedToken
from rest_framework_simplejwt.exceptions import TokenError

User = get_user_model()
logger = logging.getLogger(__name__)

class TokenSecurityReason:
    """Token blacklisting reasons for security tracking"""
    LOGOUT = 'logout'
    PASSWORD_CHANGE = 'password_change'
    ACCOUNT_COMPROMISE = 'account_compromise'
    SUSPICIOUS_ACTIVITY = 'suspicious_activity'
    ADMIN_ACTION = 'admin_action'
    TOKEN_THEFT = 'token_theft'
    DEVICE_CHANGE = 'device_change'
    EXPIRED = 'expired'
    REVOKED = 'revoked'

class TokenFamilyManager:
    """
    Manages token families for enhanced security.
    A token family represents all tokens issued from a single login session.
    """
    
    @staticmethod
    def create_token_family(user, device_info: Dict = None) -> str:
        """
        Create a new token family for a user session.
        
        Args:
            user: User instance
            device_info: Device information dictionary
            
        Returns:
            str: Token family ID
        """
        family_id = str(uuid.uuid4())
        
        # Store family info in cache for quick access
        family_data = {
            'user_id': user.id,
            'created_at': timezone.now().isoformat(),
            'device_info': device_info or {},
            'active_tokens': [],
            'blacklisted': False
        }
        
        cache.set(f'token_family:{family_id}', family_data, timeout=86400 * 7)  # 7 days
        
        logger.info(f"Created token family {family_id} for user {user.id}")
        return family_id
    
    @staticmethod
    def add_token_to_family(family_id: str, token_jti: str) -> bool:
        """
        Add a token to a family.
        
        Args:
            family_id: Token family ID
            token_jti: Token JTI claim
            
        Returns:
            bool: Success status
        """
        family_data = cache.get(f'token_family:{family_id}')
        if not family_data:
            return False
        
        family_data['active_tokens'].append({
            'jti': token_jti,
            'created_at': timezone.now().isoformat()
        })
        
        cache.set(f'token_family:{family_id}', family_data, timeout=86400 * 7)
        return True
    
    @staticmethod
    def blacklist_token_family(family_id: str, reason: str = TokenSecurityReason.SUSPICIOUS_ACTIVITY) -> int:
        """
        Blacklist an entire token family for security.
        
        Args:
            family_id: Token family ID
            reason: Blacklisting reason
            
        Returns:
            int: Number of tokens blacklisted
        """
        family_data = cache.get(f'token_family:{family_id}')
        if not family_data:
            return 0
        
        blacklisted_count = 0
        
        # Blacklist all active tokens in the family
        for token_info in family_data['active_tokens']:
            try:
                outstanding_token = OutstandingToken.objects.get(jti=token_info['jti'])
                BlacklistedToken.objects.get_or_create(token=outstanding_token)
                blacklisted_count += 1
            except OutstandingToken.DoesNotExist:
                continue
        
        # Mark family as blacklisted
        family_data['blacklisted'] = True
        family_data['blacklisted_at'] = timezone.now().isoformat()
        family_data['blacklist_reason'] = reason
        
        cache.set(f'token_family:{family_id}', family_data, timeout=86400 * 7)
        
        logger.warning(f"Blacklisted token family {family_id} with {blacklisted_count} tokens. Reason: {reason}")
        return blacklisted_count

class EnhancedTokenManager:
    """
    Enhanced token management with comprehensive security features.
    """
    
    def __init__(self):
        self.suspicious_activity_threshold = 5  # Max token refreshes per hour
        self.max_concurrent_sessions = 10  # Max concurrent sessions per user
    
    def create_token_pair(self, user, device_info: Dict = None, 
                         family_id: str = None) -> Tuple[RefreshToken, AccessToken]:
        """
        Create a new token pair with enhanced security features.
        
        Args:
            user: User instance
            device_info: Device information
            family_id: Existing family ID or None for new family
            
        Returns:
            Tuple[RefreshToken, AccessToken]: Token pair
        """
        # Create or use existing token family
        if not family_id:
            family_id = TokenFamilyManager.create_token_family(user, device_info)
        
        # Generate refresh token
        refresh = RefreshToken.for_user(user)
        access = refresh.access_token
        
        # Add custom claims
        refresh['family_id'] = family_id
        refresh['device_info'] = device_info or {}
        refresh['created_at'] = timezone.now().isoformat()
        
        access['family_id'] = family_id
        access['device_info'] = device_info or {}
        access['user_role'] = user.role
        access['is_verified'] = user.is_verified
        
        # Add token to family
        TokenFamilyManager.add_token_to_family(family_id, refresh['jti'])
        
        # Track token creation
        self._track_token_activity(user.id, 'token_created', {
            'family_id': family_id,
            'device_info': device_info
        })
        
        logger.info(f"Created token pair for user {user.id} in family {family_id}")
        return refresh, access
    
    def rotate_token(self, refresh_token_str: str, device_info: Dict = None) -> Tuple[RefreshToken, AccessToken]:
        """
        Rotate refresh token with enhanced security checks.
        
        Args:
            refresh_token_str: Current refresh token string
            device_info: Device information
            
        Returns:
            Tuple[RefreshToken, AccessToken]: New token pair
            
        Raises:
            TokenError: If token is invalid or suspicious activity detected
        """
        try:
            # Validate current token
            current_refresh = RefreshToken(refresh_token_str)
            # Get user from token payload
            user_id = current_refresh.payload.get('user_id')
            user = User.objects.get(id=user_id)
            
            # Security checks
            self._perform_security_checks(user, current_refresh, device_info)
            
            # Get family information
            family_id = current_refresh.get('family_id')
            if not family_id:
                # Legacy token without family - create new family
                family_id = TokenFamilyManager.create_token_family(user, device_info)
            
            # Create new token pair
            new_refresh, new_access = self.create_token_pair(user, device_info, family_id)
            
            # Blacklist old token
            self.blacklist_token(current_refresh['jti'], TokenSecurityReason.LOGOUT)
            
            # Track rotation
            self._track_token_activity(user.id, 'token_rotated', {
                'old_jti': current_refresh['jti'],
                'new_jti': new_refresh['jti'],
                'family_id': family_id
            })
            
            logger.info(f"Rotated token for user {user.id} in family {family_id}")
            return new_refresh, new_access
            
        except TokenError as e:
            logger.warning(f"Token rotation failed: {e}")
            raise
    
    def blacklist_token(self, token_jti: str, reason: str = TokenSecurityReason.LOGOUT,
                       user_id: int = None) -> bool:
        """
        Blacklist a specific token with reason tracking.
        
        Args:
            token_jti: Token JTI claim
            reason: Blacklisting reason
            user_id: User ID for logging
            
        Returns:
            bool: Success status
        """
        try:
            outstanding_token = OutstandingToken.objects.get(jti=token_jti)
            blacklisted_token, created = BlacklistedToken.objects.get_or_create(
                token=outstanding_token
            )
            
            if created:
                # Track blacklisting
                self._track_token_activity(user_id or outstanding_token.user_id, 'token_blacklisted', {
                    'jti': token_jti,
                    'reason': reason
                })
                
                logger.info(f"Blacklisted token {token_jti} for reason: {reason}")
                return True
            
            return False  # Already blacklisted
            
        except OutstandingToken.DoesNotExist:
            logger.warning(f"Attempted to blacklist non-existent token: {token_jti}")
            return False
    
    def blacklist_all_user_tokens(self, user_id: int, reason: str = TokenSecurityReason.ACCOUNT_COMPROMISE) -> int:
        """
        Blacklist all tokens for a specific user.
        
        Args:
            user_id: User ID
            reason: Blacklisting reason
            
        Returns:
            int: Number of tokens blacklisted
        """
        outstanding_tokens = OutstandingToken.objects.filter(user_id=user_id)
        blacklisted_count = 0
        
        for token in outstanding_tokens:
            if not BlacklistedToken.objects.filter(token=token).exists():
                BlacklistedToken.objects.create(token=token)
                blacklisted_count += 1
        
        # Track mass blacklisting
        self._track_token_activity(user_id, 'all_tokens_blacklisted', {
            'count': blacklisted_count,
            'reason': reason
        })
        
        logger.warning(f"Blacklisted {blacklisted_count} tokens for user {user_id}. Reason: {reason}")
        return blacklisted_count
    
    def is_token_blacklisted(self, token_jti: str) -> bool:
        """
        Check if a token is blacklisted.
        
        Args:
            token_jti: Token JTI claim
            
        Returns:
            bool: True if blacklisted
        """
        try:
            outstanding_token = OutstandingToken.objects.get(jti=token_jti)
            return BlacklistedToken.objects.filter(token=outstanding_token).exists()
        except OutstandingToken.DoesNotExist:
            return True  # Non-existent tokens are considered blacklisted
    
    def get_user_active_sessions(self, user_id: int) -> List[Dict]:
        """
        Get all active sessions for a user.
        
        Args:
            user_id: User ID
            
        Returns:
            List[Dict]: Active session information
        """
        outstanding_tokens = OutstandingToken.objects.filter(
            user_id=user_id
        ).exclude(
            blacklistedtoken__isnull=False
        ).order_by('-created_at')
        
        sessions = []
        for token in outstanding_tokens:
            try:
                refresh = RefreshToken(token.token)
                device_info = refresh.get('device_info', {})
                
                sessions.append({
                    'jti': token.jti,
                    'created_at': token.created_at,
                    'expires_at': token.expires_at,
                    'device_info': device_info,
                    'family_id': refresh.get('family_id')
                })
            except TokenError:
                # Token is invalid, should be cleaned up
                continue
        
        return sessions
    
    def cleanup_expired_tokens(self) -> int:
        """
        Clean up expired tokens from the database.
        
        Returns:
            int: Number of tokens cleaned up
        """
        expired_tokens = OutstandingToken.objects.filter(
            expires_at__lt=timezone.now()
        )
        
        count = expired_tokens.count()
        expired_tokens.delete()
        
        logger.info(f"Cleaned up {count} expired tokens")
        return count
    
    def _perform_security_checks(self, user, current_token: RefreshToken, device_info: Dict = None):
        """
        Perform security checks during token operations.
        
        Args:
            user: User instance
            current_token: Current refresh token
            device_info: Device information
            
        Raises:
            TokenError: If suspicious activity detected
        """
        # Check for suspicious refresh rate
        refresh_count = self._get_recent_refresh_count(user.id)
        if refresh_count > self.suspicious_activity_threshold:
            # Blacklist all user tokens
            self.blacklist_all_user_tokens(user.id, TokenSecurityReason.SUSPICIOUS_ACTIVITY)
            raise TokenError("Suspicious activity detected - all tokens revoked")
        
        # Check device consistency (if device tracking is enabled)
        if device_info:
            stored_device = current_token.get('device_info', {})
            if stored_device and self._is_device_change_suspicious(stored_device, device_info):
                logger.warning(f"Suspicious device change detected for user {user.id}")
                # Could implement additional verification here
        
        # Check concurrent session limit
        active_sessions = len(self.get_user_active_sessions(user.id))
        if active_sessions > self.max_concurrent_sessions:
            logger.warning(f"User {user.id} exceeded concurrent session limit")
            # Could implement session cleanup here
    
    def _track_token_activity(self, user_id: int, activity_type: str, metadata: Dict):
        """
        Track token-related activities for security monitoring.
        
        Args:
            user_id: User ID
            activity_type: Type of activity
            metadata: Additional metadata
        """
        activity_key = f'token_activity:{user_id}:{activity_type}'
        activities = cache.get(activity_key, [])
        
        activities.append({
            'timestamp': timezone.now().isoformat(),
            'metadata': metadata
        })
        
        # Keep only recent activities (last 24 hours)
        cutoff = timezone.now() - timedelta(hours=24)
        activities = [
            a for a in activities 
            if datetime.fromisoformat(a['timestamp'].replace('Z', '+00:00')) > cutoff
        ]
        
        cache.set(activity_key, activities, timeout=86400)
    
    def _get_recent_refresh_count(self, user_id: int) -> int:
        """
        Get recent token refresh count for suspicious activity detection.
        
        Args:
            user_id: User ID
            
        Returns:
            int: Recent refresh count
        """
        activities = cache.get(f'token_activity:{user_id}:token_rotated', [])
        
        # Count refreshes in the last hour
        cutoff = timezone.now() - timedelta(hours=1)
        recent_count = sum(
            1 for a in activities
            if datetime.fromisoformat(a['timestamp'].replace('Z', '+00:00')) > cutoff
        )
        
        return recent_count
    
    def _is_device_change_suspicious(self, old_device: Dict, new_device: Dict) -> bool:
        """
        Check if device change is suspicious.
        
        Args:
            old_device: Previous device info
            new_device: New device info
            
        Returns:
            bool: True if suspicious
        """
        # Simple device fingerprinting check
        key_fields = ['user_agent', 'ip_address', 'device_id']
        
        changes = sum(
            1 for field in key_fields
            if old_device.get(field) != new_device.get(field)
        )
        
        # If more than 2 key fields changed, it might be suspicious
        return changes > 2

# Global instance for use throughout the application
enhanced_token_manager = EnhancedTokenManager()
