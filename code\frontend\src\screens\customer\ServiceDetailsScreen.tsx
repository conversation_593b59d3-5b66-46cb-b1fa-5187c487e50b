/**
 * Service Details Screen
 * Customer-specific screen for viewing service details
 */

import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

interface ServiceDetailsScreenProps {
  route?: {
    params?: {
      serviceId?: string;
    };
  };
}

export const ServiceDetailsScreen: React.FC<ServiceDetailsScreenProps> = ({ route }) => {
  const serviceId = route?.params?.serviceId;

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content}>
        <Text style={styles.title}>Service Details</Text>
        <Text style={styles.subtitle}>Service ID: {serviceId || 'N/A'}</Text>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Description</Text>
          <Text style={styles.text}>
            This is a placeholder for the service details screen. 
            This screen will display detailed information about a specific service.
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Features</Text>
          <Text style={styles.text}>• Service information display</Text>
          <Text style={styles.text}>• Provider details</Text>
          <Text style={styles.text}>• Pricing information</Text>
          <Text style={styles.text}>• Booking functionality</Text>
          <Text style={styles.text}>• Reviews and ratings</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    marginBottom: 24,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 12,
  },
  text: {
    fontSize: 16,
    color: '#333333',
    lineHeight: 24,
    marginBottom: 8,
  },
});

export default ServiceDetailsScreen;
