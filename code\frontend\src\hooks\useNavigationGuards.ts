/**
 * Navigation Guards Hook
 * Provides navigation guard functionality with FSM-based patterns
 */

import { useCallback } from 'react';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../contexts/AuthContext';
import { navigationGuards, NavigationState, NavigationGuardResult } from '../services/navigationGuards';

export const useNavigationGuards = () => {
  const navigation = useNavigation();
  const { isAuthenticated, user, isProvider } = useAuth();

  /**
   * Get current navigation state
   */
  const getNavigationState = useCallback((): NavigationState => {
    return {
      currentRoute: navigation.getState()?.routes[navigation.getState()?.index || 0]?.name || '',
      userRole: isProvider ? 'provider' : 'customer',
      isAuthenticated,
      isOnboarded: user?.hasCompletedOnboarding || false,
      isVerified: user?.isVerified || false,
    };
  }, [navigation, isAuthenticated, user, isProvider]);

  /**
   * Navigate with guard checks
   */
  const navigateWithGuards = useCallback((routeName: string, params?: any) => {
    const navigationState = getNavigationState();
    const guardResult = navigationGuards.canNavigate(routeName, navigationState);

    if (guardResult.allowed) {
      // Navigation is allowed
      navigation.navigate(routeName as never, params as never);
      return true;
    } else {
      // Navigation is blocked
      console.warn(`Navigation to ${routeName} blocked: ${guardResult.reason}`);
      
      if (guardResult.redirectTo) {
        // Redirect to appropriate route
        navigation.navigate(guardResult.redirectTo as never);
      }
      
      return false;
    }
  }, [navigation, getNavigationState]);

  /**
   * Check if navigation to route is allowed
   */
  const canNavigateTo = useCallback((routeName: string): NavigationGuardResult => {
    const navigationState = getNavigationState();
    return navigationGuards.canNavigate(routeName, navigationState);
  }, [getNavigationState]);

  /**
   * Get default route for current user
   */
  const getDefaultRoute = useCallback((): string => {
    const navigationState = getNavigationState();
    
    if (!navigationState.isAuthenticated) {
      return 'Login';
    }
    
    if (navigationState.userRole === 'provider') {
      return 'ProviderStack';
    }
    
    return 'CustomerStack';
  }, [getNavigationState]);

  /**
   * Navigate to default route for current user
   */
  const navigateToDefault = useCallback(() => {
    const defaultRoute = getDefaultRoute();
    navigation.navigate(defaultRoute as never);
  }, [navigation, getDefaultRoute]);

  /**
   * Check if current route requires authentication
   */
  const currentRouteRequiresAuth = useCallback((): boolean => {
    const navigationState = getNavigationState();
    return navigationGuards.requiresAuth(navigationState.currentRoute);
  }, [getNavigationState]);

  /**
   * Check if current route is allowed for user role
   */
  const currentRouteAllowedForRole = useCallback((): boolean => {
    const navigationState = getNavigationState();
    return navigationGuards.isAllowedForRole(
      navigationState.currentRoute,
      navigationState.userRole || ''
    );
  }, [getNavigationState]);

  return {
    navigateWithGuards,
    canNavigateTo,
    getDefaultRoute,
    navigateToDefault,
    currentRouteRequiresAuth,
    currentRouteAllowedForRole,
    getNavigationState,
  };
};
