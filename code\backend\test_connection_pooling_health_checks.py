"""
Test suite for connection pooling and health checks implementation.

This test file is part of EPIC-AUDIT-001 - Database Configuration Overhaul
Task: Implement Connection Pooling and Health Checks
"""

import os
import sys
import unittest
from unittest.mock import patch, MagicMock
import time
from pathlib import Path
from django.test import TestCase, override_settings
from django.db import connection, connections
from django.core.management import call_command

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

class ConnectionPoolingHealthChecksTest(TestCase):
    """
    Test connection pooling and health checks functionality.
    """
    
    def setUp(self):
        """Set up test environment"""
        self.original_env = dict(os.environ)
        
    def tearDown(self):
        """Clean up test environment"""
        # Restore original environment
        os.environ.clear()
        os.environ.update(self.original_env)
        
        # Close all database connections
        connections.close_all()
    
    def test_connection_pooling_configuration(self):
        """
        Test that connection pooling is properly configured
        """
        from django.conf import settings
        
        # Check that CONN_MAX_AGE is set
        db_config = settings.DATABASES['default']
        
        if db_config['ENGINE'] == 'django.db.backends.postgresql':
            self.assertIsNotNone(db_config.get('CONN_MAX_AGE'))
            self.assertGreater(db_config.get('CONN_MAX_AGE', 0), 0)
            
            # Should be 600 seconds (10 minutes) as per configuration
            self.assertEqual(db_config.get('CONN_MAX_AGE'), 600)
            
            print("✅ Connection pooling configured correctly")
            print(f"   CONN_MAX_AGE: {db_config.get('CONN_MAX_AGE')} seconds")
        else:
            print("ℹ️ System is using SQLite - connection pooling not applicable")
    
    def test_health_checks_configuration(self):
        """
        Test that health checks are properly configured
        """
        from django.conf import settings
        
        db_config = settings.DATABASES['default']
        
        if db_config['ENGINE'] == 'django.db.backends.postgresql':
            # Check that CONN_HEALTH_CHECKS is enabled
            self.assertTrue(db_config.get('CONN_HEALTH_CHECKS', False))
            
            print("✅ Health checks configured correctly")
            print(f"   CONN_HEALTH_CHECKS: {db_config.get('CONN_HEALTH_CHECKS')}")
        else:
            print("ℹ️ System is using SQLite - health checks not applicable")
    
    def test_connection_reuse_behavior(self):
        """
        Test that database connections are reused within the connection pool
        """
        from django.db import connection
        
        # Get initial connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            initial_connection_id = id(connection.connection)
        
        # Make another query - should reuse the same connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            second_connection_id = id(connection.connection)
        
        # Connection should be reused
        self.assertEqual(initial_connection_id, second_connection_id)
        
        print("✅ Connection reuse working correctly")
        print(f"   Connection ID: {initial_connection_id}")
    
    def test_connection_health_check_on_reuse(self):
        """
        Test that health checks are performed when reusing connections
        """
        from django.db import connection
        
        # This test verifies that Django performs health checks
        # when CONN_HEALTH_CHECKS is enabled
        
        # Make initial connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            self.assertEqual(result[0], 1)
        
        # Simulate connection being idle (in real scenario, this would be longer)
        # Django should perform health check before reusing
        
        # Make another query - health check should occur
        with connection.cursor() as cursor:
            cursor.execute("SELECT version()")
            result = cursor.fetchone()
            self.assertIsNotNone(result)
        
        print("✅ Health check mechanism working")
    
    def test_connection_timeout_behavior(self):
        """
        Test connection timeout behavior with CONN_MAX_AGE
        """
        from django.db import connection
        from django.conf import settings
        
        db_config = settings.DATABASES['default']
        
        if db_config['ENGINE'] == 'django.db.backends.postgresql':
            # Get the configured max age
            max_age = db_config.get('CONN_MAX_AGE', 0)
            
            # Make initial connection
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                initial_connection = connection.connection
            
            # Check that connection has the right properties
            self.assertIsNotNone(initial_connection)
            
            # In a real scenario, we would wait for max_age seconds
            # For testing, we just verify the configuration is applied
            
            print(f"✅ Connection timeout configured for {max_age} seconds")
        else:
            print("ℹ️ System is using SQLite - connection timeout not applicable")
    
    def test_multiple_concurrent_connections(self):
        """
        Test behavior with multiple concurrent database connections
        """
        from django.db import connections
        
        # Test that we can handle multiple connections
        default_conn = connections['default']
        
        # Make queries on default connection
        with default_conn.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            self.assertEqual(result[0], 1)
        
        # Verify connection is managed properly
        self.assertIsNotNone(default_conn.connection)
        
        print("✅ Multiple connection handling working")
    
    def test_connection_error_recovery(self):
        """
        Test that the system recovers gracefully from connection errors
        """
        from django.db import connection
        from django.db.utils import OperationalError
        
        # Test that we can handle connection errors gracefully
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                self.assertEqual(result[0], 1)
            
            # Connection should be working
            self.assertTrue(True)
            print("✅ Connection error recovery mechanism available")
            
        except OperationalError as e:
            # If we get an operational error, it should be handled gracefully
            print(f"ℹ️ Connection error handled: {e}")
    
    def test_database_connection_optimization_settings(self):
        """
        Test that database optimization settings are properly applied
        """
        from django.conf import settings
        
        db_config = settings.DATABASES['default']
        
        if db_config['ENGINE'] == 'django.db.backends.postgresql':
            # Test optimization settings
            self.assertFalse(db_config.get('ATOMIC_REQUESTS', True))
            
            # Test connection options
            options = db_config.get('OPTIONS', {})
            self.assertIn('connect_timeout', options)
            self.assertEqual(options['connect_timeout'], 10)
            
            # Test transaction isolation
            self.assertIn('options', options)
            self.assertIn('default_transaction_isolation=read_committed', options['options'])
            
            print("✅ Database optimization settings applied")
            print(f"   ATOMIC_REQUESTS: {db_config.get('ATOMIC_REQUESTS')}")
            print(f"   Connect Timeout: {options.get('connect_timeout')}")
            print(f"   Transaction Isolation: read_committed")
        else:
            print("ℹ️ System is using SQLite - optimization settings not applicable")
    
    def test_connection_pool_stress_test(self):
        """
        Test connection pool under stress conditions
        """
        from django.db import connection
        
        # Perform multiple rapid queries to test connection pooling
        query_count = 10
        connection_ids = []
        
        for i in range(query_count):
            with connection.cursor() as cursor:
                cursor.execute("SELECT %s", [i])
                result = cursor.fetchone()
                self.assertEqual(result[0], i)
                
                # Track connection IDs to verify reuse
                if connection.connection:
                    connection_ids.append(id(connection.connection))
        
        # With connection pooling, we should see connection reuse
        if connection_ids:
            unique_connections = len(set(connection_ids))
            print(f"✅ Stress test completed: {query_count} queries, {unique_connections} unique connections")
            
            # With proper pooling, we should have fewer unique connections than queries
            self.assertLessEqual(unique_connections, query_count)
        else:
            print("ℹ️ Connection pooling test not applicable (SQLite)")
    
    def test_health_check_query_execution(self):
        """
        Test that health check queries can be executed successfully
        """
        from django.db import connection
        
        # Test various health check queries
        health_check_queries = [
            "SELECT 1",
            "SELECT version()",
            "SELECT current_timestamp",
        ]
        
        for query in health_check_queries:
            try:
                with connection.cursor() as cursor:
                    cursor.execute(query)
                    result = cursor.fetchone()
                    self.assertIsNotNone(result)
                
                print(f"✅ Health check query successful: {query}")
                
            except Exception as e:
                print(f"⚠️ Health check query failed: {query} - {e}")
    
    def test_connection_pool_configuration_validation(self):
        """
        Test that connection pool configuration is valid and consistent
        """
        from django.conf import settings
        
        db_config = settings.DATABASES['default']
        
        # Validate configuration consistency
        if db_config['ENGINE'] == 'django.db.backends.postgresql':
            conn_max_age = db_config.get('CONN_MAX_AGE', 0)
            conn_health_checks = db_config.get('CONN_HEALTH_CHECKS', False)
            
            # If connection pooling is enabled, health checks should also be enabled
            if conn_max_age > 0:
                self.assertTrue(conn_health_checks, 
                               "Health checks should be enabled when connection pooling is active")
            
            # Validate reasonable values
            self.assertGreaterEqual(conn_max_age, 0)
            self.assertLessEqual(conn_max_age, 3600)  # Max 1 hour
            
            print("✅ Connection pool configuration validation passed")
            print(f"   Configuration is consistent and within reasonable limits")
        else:
            print("ℹ️ PostgreSQL configuration validation skipped (using SQLite)")


class ConnectionPoolingIntegrationTest(TestCase):
    """
    Integration tests for connection pooling with actual database operations
    """
    
    def test_connection_pooling_with_migrations(self):
        """
        Test that connection pooling works correctly with Django migrations
        """
        from django.core.management import call_command
        from django.db import connection
        
        # Test that migrations work with connection pooling
        try:
            # Run a simple migration check
            call_command('showmigrations', verbosity=0)
            
            # Verify connection is still working
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                self.assertEqual(result[0], 1)
            
            print("✅ Connection pooling compatible with migrations")
            
        except Exception as e:
            print(f"⚠️ Migration compatibility issue: {e}")
    
    def test_connection_pooling_with_transactions(self):
        """
        Test that connection pooling works correctly with database transactions
        """
        from django.db import transaction, connection
        
        # Test transaction handling with connection pooling
        try:
            with transaction.atomic():
                with connection.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    result = cursor.fetchone()
                    self.assertEqual(result[0], 1)
            
            # Test that connection is still usable after transaction
            with connection.cursor() as cursor:
                cursor.execute("SELECT 2")
                result = cursor.fetchone()
                self.assertEqual(result[0], 2)
            
            print("✅ Connection pooling compatible with transactions")
            
        except Exception as e:
            print(f"⚠️ Transaction compatibility issue: {e}")


if __name__ == '__main__':
    # Run the connection pooling and health checks tests
    unittest.main(verbosity=2)
