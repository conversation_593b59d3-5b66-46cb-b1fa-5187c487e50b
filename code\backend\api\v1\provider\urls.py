"""
Provider API v1 URLs
Provider-specific endpoints for service providers
"""

from django.urls import path, include
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework import status

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def provider_dashboard(request):
    """Provider dashboard endpoint"""
    if not request.user.is_service_provider:
        return Response(
            {'error': 'Access denied. Service provider role required.'},
            status=status.HTTP_403_FORBIDDEN
        )
    
    return Response({
        'message': 'Provider dashboard',
        'user': request.user.get_full_name(),
        'role': request.user.role,
        'endpoints': {
            'profile': '/api/v1/provider/profile/',
            'services': '/api/v1/provider/services/',
            'bookings': '/api/v1/provider/bookings/',
        }
    })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def provider_profile(request):
    """Provider profile endpoint"""
    if not request.user.is_service_provider:
        return Response(
            {'error': 'Access denied. Service provider role required.'},
            status=status.HTTP_403_FORBIDDEN
        )
    
    # Redirect to existing profile endpoint for now
    from authentication.views import UserProfileView
    view = UserProfileView()
    view.request = request
    return view.get(request)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def provider_services(request):
    """Provider services endpoint"""
    if not request.user.is_service_provider:
        return Response(
            {'error': 'Access denied. Service provider role required.'},
            status=status.HTTP_403_FORBIDDEN
        )
    
    return Response({
        'message': 'Provider services',
        'services': [],  # TODO: Implement services logic
        'count': 0
    })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def provider_bookings(request):
    """Provider bookings endpoint"""
    if not request.user.is_service_provider:
        return Response(
            {'error': 'Access denied. Service provider role required.'},
            status=status.HTTP_403_FORBIDDEN
        )
    
    return Response({
        'message': 'Provider bookings',
        'bookings': [],  # TODO: Implement bookings logic
        'count': 0
    })

@api_view(['GET'])
@permission_classes([AllowAny])
def provider_root(request):
    """Provider API root endpoint"""
    return Response({
        'message': 'Provider API v1',
        'endpoints': {
            'dashboard': '/api/v1/provider/dashboard/',
            'profile': '/api/v1/provider/profile/',
            'services': '/api/v1/provider/services/',
            'bookings': '/api/v1/provider/bookings/',
        }
    })

urlpatterns = [
    # Provider root
    path('', provider_root, name='provider_root'),

    # Provider-specific endpoints
    path('dashboard/', provider_dashboard, name='provider_dashboard'),
    path('profile/', provider_profile, name='provider_profile'),
    path('services/', provider_services, name='provider_services'),
    path('bookings/', provider_bookings, name='provider_bookings'),
]
