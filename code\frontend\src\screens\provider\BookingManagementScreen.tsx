/**
 * Booking Management Screen
 * Provider screen for managing bookings and appointments
 */

import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export const BookingManagementScreen: React.FC = () => {
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content}>
        <Text style={styles.title}>Booking Management</Text>
        <Text style={styles.subtitle}>Manage your appointments and bookings</Text>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Booking Overview</Text>
          <Text style={styles.text}>
            This is a placeholder for the booking management screen. 
            This screen will allow providers to view and manage all their bookings.
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Features</Text>
          <Text style={styles.text}>• View upcoming appointments</Text>
          <Text style={styles.text}>• Accept or decline booking requests</Text>
          <Text style={styles.text}>• Reschedule appointments</Text>
          <Text style={styles.text}>• Communicate with customers</Text>
          <Text style={styles.text}>• Mark services as completed</Text>
          <Text style={styles.text}>• View booking history</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Today's Schedule</Text>
          <View style={styles.scheduleCard}>
            <Text style={styles.scheduleTitle}>No appointments today</Text>
            <Text style={styles.scheduleDescription}>
              Your schedule is clear for today
            </Text>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Pending Requests</Text>
          <View style={styles.requestCard}>
            <Text style={styles.requestTitle}>No pending requests</Text>
            <Text style={styles.requestDescription}>
              All booking requests have been processed
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    marginBottom: 24,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 12,
  },
  text: {
    fontSize: 16,
    color: '#333333',
    lineHeight: 24,
    marginBottom: 8,
  },
  scheduleCard: {
    backgroundColor: '#E8F5E8',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#C3E6C3',
  },
  scheduleTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2D5A2D',
    marginBottom: 4,
  },
  scheduleDescription: {
    fontSize: 14,
    color: '#5A7A5A',
  },
  requestCard: {
    backgroundColor: '#FFF3E0',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#FFE0B2',
  },
  requestTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#E65100',
    marginBottom: 4,
  },
  requestDescription: {
    fontSize: 14,
    color: '#BF6000',
  },
});

export default BookingManagementScreen;
