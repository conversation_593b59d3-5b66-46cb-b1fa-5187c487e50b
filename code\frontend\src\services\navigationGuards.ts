/**
 * Navigation Guards Service
 * Implements FSM-based navigation patterns with role-based access control
 * Based on reference implementation
 */

import { useAuth } from '../contexts/AuthContext';

// Navigation guard result interface
export interface NavigationGuardResult {
  allowed: boolean;
  redirectTo?: string;
  reason?: string;
  requiresAuth?: boolean;
  requiresRole?: 'customer' | 'provider';
}

// Route configuration interface
export interface RouteConfig {
  requiresAuth: boolean;
  allowedRoles?: ('customer' | 'provider')[];
  requiresOnboarding?: boolean;
  requiresVerification?: boolean;
}

// Navigation state interface
export interface NavigationState {
  currentRoute: string;
  userRole: 'customer' | 'provider' | null;
  isAuthenticated: boolean;
  isOnboarded: boolean;
  isVerified: boolean;
}

/**
 * Navigation Guards Service
 * Manages route access control and navigation state
 */
class NavigationGuardsService {
  private routeConfigs: Map<string, RouteConfig> = new Map();

  constructor() {
    this.initializeRouteConfigs();
  }

  /**
   * Initialize route configurations
   */
  private initializeRouteConfigs(): void {
    // Public routes (no authentication required)
    this.routeConfigs.set('Auth', {
      requiresAuth: false,
    });

    this.routeConfigs.set('Login', {
      requiresAuth: false,
    });

    this.routeConfigs.set('Register', {
      requiresAuth: false,
    });

    this.routeConfigs.set('ForgotPassword', {
      requiresAuth: false,
    });

    // Customer-specific routes
    this.routeConfigs.set('CustomerStack', {
      requiresAuth: true,
      allowedRoles: ['customer'],
    });

    this.routeConfigs.set('CustomerTabs', {
      requiresAuth: true,
      allowedRoles: ['customer'],
    });

    this.routeConfigs.set('Home', {
      requiresAuth: true,
      allowedRoles: ['customer'],
    });

    this.routeConfigs.set('Services', {
      requiresAuth: true,
      allowedRoles: ['customer'],
    });

    this.routeConfigs.set('ServiceDetails', {
      requiresAuth: true,
      allowedRoles: ['customer'],
    });

    this.routeConfigs.set('Bookings', {
      requiresAuth: true,
      allowedRoles: ['customer'],
    });

    this.routeConfigs.set('BookingDetails', {
      requiresAuth: true,
      allowedRoles: ['customer'],
    });

    // Provider-specific routes
    this.routeConfigs.set('ProviderStack', {
      requiresAuth: true,
      allowedRoles: ['provider'],
    });

    this.routeConfigs.set('ProviderTabs', {
      requiresAuth: true,
      allowedRoles: ['provider'],
    });

    this.routeConfigs.set('Dashboard', {
      requiresAuth: true,
      allowedRoles: ['provider'],
    });

    this.routeConfigs.set('ServiceManagement', {
      requiresAuth: true,
      allowedRoles: ['provider'],
    });

    this.routeConfigs.set('BookingManagement', {
      requiresAuth: true,
      allowedRoles: ['provider'],
    });

    this.routeConfigs.set('ProviderAnalytics', {
      requiresAuth: true,
      allowedRoles: ['provider'],
    });

    this.routeConfigs.set('CustomerManagement', {
      requiresAuth: true,
      allowedRoles: ['provider'],
    });

    // Shared routes (both customer and provider)
    this.routeConfigs.set('Profile', {
      requiresAuth: true,
      allowedRoles: ['customer', 'provider'],
    });

    this.routeConfigs.set('EditProfile', {
      requiresAuth: true,
      allowedRoles: ['customer', 'provider'],
    });

    this.routeConfigs.set('Settings', {
      requiresAuth: true,
      allowedRoles: ['customer', 'provider'],
    });

    this.routeConfigs.set('Notifications', {
      requiresAuth: true,
      allowedRoles: ['customer', 'provider'],
    });
  }

  /**
   * Check if navigation to a route is allowed
   */
  canNavigate(routeName: string, navigationState: NavigationState): NavigationGuardResult {
    const config = this.routeConfigs.get(routeName);

    if (!config) {
      // Route not configured - allow by default but log warning
      console.warn(`Navigation guard: Route '${routeName}' not configured`);
      return { allowed: true };
    }

    const { isAuthenticated, userRole } = navigationState;

    // Check authentication requirement
    if (config.requiresAuth && !isAuthenticated) {
      return {
        allowed: false,
        redirectTo: 'Login',
        reason: 'Authentication required',
        requiresAuth: true,
      };
    }

    // Check role requirement
    if (config.allowedRoles && config.allowedRoles.length > 0) {
      if (!userRole || !config.allowedRoles.includes(userRole)) {
        return {
          allowed: false,
          redirectTo: this.getDefaultRouteForRole(userRole),
          reason: 'Insufficient role permissions',
          requiresRole: config.allowedRoles[0],
        };
      }
    }

    // Check onboarding requirement
    if (config.requiresOnboarding && !navigationState.isOnboarded) {
      return {
        allowed: false,
        redirectTo: userRole === 'customer' ? 'CustomerOnboarding' : 'ProviderOnboarding',
        reason: 'Onboarding required',
      };
    }

    // Check verification requirement
    if (config.requiresVerification && !navigationState.isVerified) {
      return {
        allowed: false,
        redirectTo: 'EmailVerification',
        reason: 'Email verification required',
      };
    }

    return { allowed: true };
  }

  /**
   * Get default route for user role
   */
  private getDefaultRouteForRole(userRole: string | null): string {
    switch (userRole) {
      case 'customer':
        return 'CustomerStack';
      case 'provider':
        return 'ProviderStack';
      default:
        return 'Login';
    }
  }

  /**
   * Get route configuration
   */
  getRouteConfig(routeName: string): RouteConfig | undefined {
    return this.routeConfigs.get(routeName);
  }

  /**
   * Check if route requires authentication
   */
  requiresAuth(routeName: string): boolean {
    const config = this.routeConfigs.get(routeName);
    return config?.requiresAuth || false;
  }

  /**
   * Check if route is allowed for user role
   */
  isAllowedForRole(routeName: string, userRole: string): boolean {
    const config = this.routeConfigs.get(routeName);
    if (!config || !config.allowedRoles) return true;
    return config.allowedRoles.includes(userRole as 'customer' | 'provider');
  }

  /**
   * Get navigation state from auth context
   */
  getNavigationState(): NavigationState {
    // This would typically use a hook, but for the service we'll return a default
    // The actual implementation would get this from the auth context
    return {
      currentRoute: '',
      userRole: null,
      isAuthenticated: false,
      isOnboarded: false,
      isVerified: false,
    };
  }
}

// Export singleton instance
export const navigationGuards = new NavigationGuardsService();
export default navigationGuards;
