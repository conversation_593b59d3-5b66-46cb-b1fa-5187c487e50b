
If automatic setup failed, please follow these manual steps:

1. Open PostgreSQL command line (psql) as superuser:
   psql -U postgres

2. Run these SQL commands:
   CREATE USER vierla_user WITH PASSWORD 'vierla_password';
   CREATE DATABASE vierla_db OWNER vierla_user;
   GRANT ALL PRIVILEGES ON DATABASE vierla_db TO vierla_user;

3. Test the connection:
   psql -U vierla_user -d vierla_db -h localhost

4. Restart the Django development server

Alternative: Use the generated SQL script:
   psql -U postgres -f setup_postgresql_database.sql
