"""
Advanced Rate Limiting and DDoS Protection Middleware

This middleware provides comprehensive rate limiting and DDoS protection
with Redis-based storage, IP blocking, and sophisticated attack detection.

Part of EPIC-AUDIT-002 - Production Security Implementation.

Features:
- Multi-tier rate limiting (IP, user, endpoint-specific)
- DDoS protection with automatic IP blocking
- Burst protection and adaptive rate limiting
- Whitelist/blacklist management
- Request pattern analysis
- Comprehensive security headers
"""

import logging
import time
import json
from typing import Dict, Optional
from django.http import JsonResponse, HttpResponse
from django.utils.deprecation import MiddlewareMixin
from django.conf import settings
from django.contrib.auth.models import AnonymousUser
from django.urls import resolve
from django.utils import timezone
from vierla_project.utils.rate_limiting import (
    rate_limiter, 
    ip_manager, 
    ddos_protector,
    RateLimitExceeded
)

logger = logging.getLogger(__name__)

class AdvancedRateLimitingMiddleware(MiddlewareMixin):
    """
    Advanced rate limiting middleware with DDoS protection.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        
        # Load rate limiting configuration
        self.config = getattr(settings, 'ADVANCED_RATE_LIMITING', {})
        
        # Default rate limits
        self.default_limits = {
            'anonymous': {'limit': 100, 'window': 3600, 'burst': 20},  # 100/hour, 20/minute burst
            'authenticated': {'limit': 1000, 'window': 3600, 'burst': 50},  # 1000/hour, 50/minute burst
            'premium': {'limit': 5000, 'window': 3600, 'burst': 100},  # 5000/hour, 100/minute burst
        }
        
        # Endpoint-specific limits
        self.endpoint_limits = {
            'auth_login': {'limit': 10, 'window': 600, 'burst': 5},  # 10/10min, 5/minute burst
            'auth_register': {'limit': 5, 'window': 3600, 'burst': 2},  # 5/hour, 2/minute burst
            'password_reset': {'limit': 3, 'window': 3600, 'burst': 1},  # 3/hour, 1/minute burst
            'search': {'limit': 200, 'window': 3600, 'burst': 30},  # 200/hour, 30/minute burst
            'upload': {'limit': 50, 'window': 3600, 'burst': 10},  # 50/hour, 10/minute burst
        }
        
        # Merge with settings configuration
        self.default_limits.update(self.config.get('default_limits', {}))
        self.endpoint_limits.update(self.config.get('endpoint_limits', {}))
        
        # DDoS protection settings
        self.ddos_enabled = self.config.get('ddos_protection', True)
        self.auto_block_enabled = self.config.get('auto_block', True)
        
        super().__init__(get_response)
    
    def process_request(self, request):
        """Process incoming request for rate limiting and DDoS protection."""
        try:
            # Get client information
            client_ip = self._get_client_ip(request)
            user = getattr(request, 'user', AnonymousUser())
            
            # Skip rate limiting for whitelisted IPs
            if ip_manager.is_ip_whitelisted(client_ip):
                return None
            
            # Check if IP is blocked
            if ip_manager.is_ip_blocked(client_ip):
                logger.warning(f"Blocked IP {client_ip} attempted access")
                return self._create_blocked_response(client_ip)
            
            # DDoS protection analysis
            if self.ddos_enabled:
                request_info = self._extract_request_info(request)
                ddos_analysis = ddos_protector.analyze_request_pattern(client_ip, request_info)
                
                if ddos_analysis['action'] == 'block':
                    if self.auto_block_enabled:
                        ip_manager.block_ip(
                            client_ip, 
                            f"DDoS protection: {'; '.join(ddos_analysis['reasons'])}", 
                            ddos_analysis['block_duration']
                        )
                    
                    logger.warning(f"DDoS protection blocked IP {client_ip}: {ddos_analysis['reasons']}")
                    return self._create_ddos_response(client_ip, ddos_analysis)
            
            # Apply rate limiting
            rate_limit_result = self._apply_rate_limiting(request, client_ip, user)
            
            if not rate_limit_result['allowed']:
                return self._create_rate_limit_response(rate_limit_result)
            
            # Store rate limit info for response headers
            request._rate_limit_info = rate_limit_result
            
            return None
            
        except Exception as e:
            logger.error(f"Error in rate limiting middleware: {e}")
            # Allow request to proceed if middleware fails
            return None
    
    def process_response(self, request, response):
        """Add rate limiting headers to response."""
        try:
            # Add security headers
            self._add_security_headers(response)
            
            # Add rate limiting headers if available
            if hasattr(request, '_rate_limit_info'):
                self._add_rate_limit_headers(response, request._rate_limit_info)
            
            return response
            
        except Exception as e:
            logger.error(f"Error in rate limiting response processing: {e}")
            return response
    
    def _apply_rate_limiting(self, request, client_ip: str, user) -> Dict:
        """Apply rate limiting based on user type and endpoint."""
        try:
            # Determine user tier
            user_tier = self._get_user_tier(user)
            
            # Get endpoint-specific limits
            endpoint_type = self._get_endpoint_type(request)
            
            # Choose appropriate limits
            if endpoint_type and endpoint_type in self.endpoint_limits:
                limits = self.endpoint_limits[endpoint_type]
                rate_key = f"endpoint:{endpoint_type}:{client_ip}"
            else:
                limits = self.default_limits[user_tier]
                rate_key = f"user:{user_tier}:{client_ip}"
            
            # Add user-specific rate limiting for authenticated users
            if not isinstance(user, AnonymousUser):
                user_key = f"user:{user.id}"
                user_allowed, user_info = rate_limiter.is_allowed(
                    user_key,
                    limits['limit'],
                    limits['window'],
                    limits.get('burst')
                )
                
                if not user_allowed:
                    return {
                        'allowed': False,
                        'limit_type': 'user',
                        'user_tier': user_tier,
                        'endpoint_type': endpoint_type,
                        **user_info
                    }
            
            # Apply IP-based rate limiting
            ip_allowed, ip_info = rate_limiter.is_allowed(
                rate_key,
                limits['limit'],
                limits['window'],
                limits.get('burst')
            )
            
            return {
                'allowed': ip_allowed,
                'limit_type': 'ip' if not ip_allowed else None,
                'user_tier': user_tier,
                'endpoint_type': endpoint_type,
                **ip_info
            }
            
        except Exception as e:
            logger.error(f"Error applying rate limiting: {e}")
            return {'allowed': True, 'error': str(e)}
    
    def _get_user_tier(self, user) -> str:
        """Determine user tier for rate limiting."""
        if isinstance(user, AnonymousUser):
            return 'anonymous'
        
        # Check for premium/subscription status
        if hasattr(user, 'subscription') and user.subscription.is_premium:
            return 'premium'
        
        return 'authenticated'
    
    def _get_endpoint_type(self, request) -> Optional[str]:
        """Determine endpoint type for specific rate limiting."""
        try:
            path = request.path.lower()
            
            # Authentication endpoints
            if '/auth/login' in path or '/auth/enhanced/login' in path:
                return 'auth_login'
            elif '/auth/register' in path:
                return 'auth_register'
            elif '/auth/password' in path and 'reset' in path:
                return 'password_reset'
            
            # API endpoints
            elif '/search' in path:
                return 'search'
            elif '/upload' in path or request.content_type and 'multipart' in request.content_type:
                return 'upload'
            
            return None
            
        except Exception as e:
            logger.error(f"Error determining endpoint type: {e}")
            return None
    
    def _extract_request_info(self, request) -> Dict:
        """Extract request information for DDoS analysis."""
        return {
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'endpoint': request.path,
            'method': request.method,
            'content_type': request.content_type,
            'query_params': dict(request.GET),
            'timestamp': time.time()
        }
    
    def _get_client_ip(self, request) -> str:
        """Get client IP address from request."""
        # Check for forwarded headers (load balancer, proxy)
        forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if forwarded_for:
            # Take the first IP in the chain
            return forwarded_for.split(',')[0].strip()
        
        # Check for real IP header
        real_ip = request.META.get('HTTP_X_REAL_IP')
        if real_ip:
            return real_ip
        
        # Fallback to remote address
        return request.META.get('REMOTE_ADDR', '127.0.0.1')
    
    def _create_rate_limit_response(self, rate_info: Dict) -> JsonResponse:
        """Create rate limit exceeded response."""
        retry_after = rate_info.get('retry_after', 3600)
        
        response_data = {
            'error': 'Rate limit exceeded',
            'message': f"Too many requests. Limit: {rate_info.get('limit', 'N/A')} per {rate_info.get('window', 'N/A')} seconds",
            'limit_type': rate_info.get('limit_type', 'unknown'),
            'retry_after': retry_after,
            'current_usage': rate_info.get('current', 0),
            'limit': rate_info.get('limit', 0)
        }
        
        response = JsonResponse(response_data, status=429)
        response['Retry-After'] = str(retry_after)
        
        return response
    
    def _create_blocked_response(self, client_ip: str) -> JsonResponse:
        """Create blocked IP response."""
        response_data = {
            'error': 'Access denied',
            'message': 'Your IP address has been temporarily blocked due to suspicious activity',
            'blocked_ip': client_ip,
            'contact': 'Please contact support if you believe this is an error'
        }
        
        response = JsonResponse(response_data, status=403)
        response['X-Blocked-IP'] = client_ip
        
        return response
    
    def _create_ddos_response(self, client_ip: str, analysis: Dict) -> JsonResponse:
        """Create DDoS protection response."""
        response_data = {
            'error': 'DDoS protection activated',
            'message': 'Suspicious activity detected. Access temporarily restricted.',
            'threat_level': analysis.get('threat_level', 'unknown'),
            'block_duration': analysis.get('block_duration', 0)
        }
        
        response = JsonResponse(response_data, status=429)
        response['X-Threat-Level'] = analysis.get('threat_level', 'unknown')
        
        return response
    
    def _add_security_headers(self, response: HttpResponse):
        """Add security headers to response."""
        security_headers = {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Referrer-Policy': 'strict-origin-when-cross-origin',
            'X-Rate-Limit-Policy': 'advanced'
        }
        
        for header, value in security_headers.items():
            if header not in response:
                response[header] = value
    
    def _add_rate_limit_headers(self, response: HttpResponse, rate_info: Dict):
        """Add rate limiting headers to response."""
        try:
            response['X-RateLimit-Limit'] = str(rate_info.get('limit', 0))
            response['X-RateLimit-Remaining'] = str(max(0, rate_info.get('limit', 0) - rate_info.get('current', 0)))
            response['X-RateLimit-Reset'] = str(int(time.time() + rate_info.get('window', 3600)))
            response['X-RateLimit-Window'] = str(rate_info.get('window', 3600))
            
            if rate_info.get('burst_limit'):
                response['X-RateLimit-Burst'] = str(rate_info.get('burst_limit', 0))
            
            if rate_info.get('user_tier'):
                response['X-RateLimit-Tier'] = rate_info['user_tier']
            
        except Exception as e:
            logger.error(f"Error adding rate limit headers: {e}")

class AuthenticationRateLimitingMiddleware(MiddlewareMixin):
    """
    Specialized middleware for authentication endpoint rate limiting.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        
        # Authentication-specific limits
        self.auth_limits = {
            'login_attempts': {'limit': 5, 'window': 900, 'block_duration': 1800},  # 5 attempts per 15min, block 30min
            'failed_login_attempts': {'limit': 3, 'window': 300, 'block_duration': 900},  # 3 failed per 5min, block 15min
            'password_reset_attempts': {'limit': 3, 'window': 3600, 'block_duration': 3600},  # 3 per hour, block 1 hour
        }
        
        super().__init__(get_response)
    
    def process_request(self, request):
        """Process authentication requests with specialized rate limiting."""
        if not self._is_auth_endpoint(request):
            return None
        
        try:
            client_ip = self._get_client_ip(request)
            
            # Check if IP is blocked for authentication
            if self._is_auth_blocked(client_ip):
                return self._create_auth_blocked_response()
            
            # Apply authentication-specific rate limiting
            auth_type = self._get_auth_type(request)
            if auth_type:
                limits = self.auth_limits.get(auth_type, self.auth_limits['login_attempts'])
                
                allowed, info = rate_limiter.is_allowed(
                    f"auth:{auth_type}:{client_ip}",
                    limits['limit'],
                    limits['window']
                )
                
                if not allowed:
                    # Block IP for authentication if limit exceeded
                    ip_manager.block_ip(
                        client_ip,
                        f"Authentication rate limit exceeded: {auth_type}",
                        limits['block_duration']
                    )
                    
                    return self._create_auth_rate_limit_response(auth_type, info)
            
            return None
            
        except Exception as e:
            logger.error(f"Error in authentication rate limiting: {e}")
            return None
    
    def _is_auth_endpoint(self, request) -> bool:
        """Check if request is to an authentication endpoint."""
        auth_paths = ['/auth/', '/api/auth/', '/login', '/register', '/password']
        return any(path in request.path.lower() for path in auth_paths)
    
    def _get_auth_type(self, request) -> Optional[str]:
        """Determine authentication type."""
        path = request.path.lower()
        
        if 'login' in path:
            return 'login_attempts'
        elif 'password' in path and 'reset' in path:
            return 'password_reset_attempts'
        elif 'register' in path:
            return 'login_attempts'  # Use same limits as login
        
        return None
    
    def _is_auth_blocked(self, client_ip: str) -> bool:
        """Check if IP is blocked for authentication."""
        return ip_manager.is_ip_blocked(client_ip)
    
    def _get_client_ip(self, request) -> str:
        """Get client IP address."""
        forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if forwarded_for:
            return forwarded_for.split(',')[0].strip()
        return request.META.get('REMOTE_ADDR', '127.0.0.1')
    
    def _create_auth_blocked_response(self) -> JsonResponse:
        """Create authentication blocked response."""
        return JsonResponse({
            'error': 'Authentication temporarily blocked',
            'message': 'Too many failed authentication attempts. Please try again later.',
            'retry_after': 1800  # 30 minutes
        }, status=429)
    
    def _create_auth_rate_limit_response(self, auth_type: str, info: Dict) -> JsonResponse:
        """Create authentication rate limit response."""
        return JsonResponse({
            'error': 'Authentication rate limit exceeded',
            'message': f'Too many {auth_type.replace("_", " ")}. Please try again later.',
            'retry_after': info.get('retry_after', 900),
            'limit': info.get('limit', 0),
            'window': info.get('window', 0)
        }, status=429)
