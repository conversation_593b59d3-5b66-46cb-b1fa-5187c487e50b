#!/usr/bin/env python3
"""
Enhanced Token Management Test Suite

This script tests the comprehensive token rotation and blacklisting system.
Part of EPIC-AUDIT-002 - Production Security Implementation.

Tests:
- Token family management
- Enhanced token rotation
- Comprehensive blacklisting
- Security checks and monitoring
- Session management
- Device tracking
"""

import os
import sys
import django
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'vierla_project.settings.development')
django.setup()

import json
from datetime import datetime, timedelta
from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils import timezone
from rest_framework.test import APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.token_blacklist.models import OutstandingToken, BlacklistedToken
from vierla_project.utils.token_management import (
    enhanced_token_manager,
    TokenFamilyManager,
    TokenSecurityReason
)

User = get_user_model()

class EnhancedTokenManagementTest(TestCase):
    """
    Test enhanced token management functionality.
    """
    
    def setUp(self):
        """Set up test environment"""
        self.client = APIClient()
        
        # Create test user
        self.user = User.objects.create_user(
            username='token_test_user',
            email='<EMAIL>',
            password='testpassword123',
            first_name='Token',
            last_name='Test',
            is_verified=True
        )
        
        # Sample device info
        self.device_info = {
            'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)',
            'ip_address': '*************',
            'device_id': 'test-device-123',
            'device_type': 'mobile',
            'app_version': '1.0.0',
            'platform': 'ios'
        }
    
    def tearDown(self):
        """Clean up test environment"""
        # Clean up test user and related tokens
        User.objects.filter(username='token_test_user').delete()
        
        # Clean up any outstanding tokens
        OutstandingToken.objects.filter(user=self.user).delete()
    
    def test_token_family_creation(self):
        """Test token family creation and management."""
        print(f"\n=== TOKEN FAMILY CREATION TEST ===")
        
        # Create token family
        family_id = TokenFamilyManager.create_token_family(self.user, self.device_info)
        
        self.assertIsNotNone(family_id)
        self.assertIsInstance(family_id, str)
        
        print(f"✅ Token family created: {family_id}")
        
        # Test adding token to family
        test_jti = 'test-jti-123'
        success = TokenFamilyManager.add_token_to_family(family_id, test_jti)
        
        self.assertTrue(success)
        print(f"✅ Token added to family successfully")
        
        return family_id
    
    def test_enhanced_token_creation(self):
        """Test enhanced token pair creation."""
        print(f"\n=== ENHANCED TOKEN CREATION TEST ===")
        
        # Create token pair
        refresh_token, access_token = enhanced_token_manager.create_token_pair(
            user=self.user,
            device_info=self.device_info
        )
        
        self.assertIsNotNone(refresh_token)
        self.assertIsNotNone(access_token)
        
        # Verify custom claims
        self.assertIn('family_id', refresh_token.payload)
        self.assertIn('device_info', refresh_token.payload)
        self.assertIn('family_id', access_token.payload)
        self.assertIn('user_role', access_token.payload)
        
        print(f"✅ Enhanced token pair created successfully")
        print(f"   Family ID: {refresh_token['family_id']}")
        print(f"   User Role: {access_token['user_role']}")
        
        return refresh_token, access_token
    
    def test_token_rotation(self):
        """Test token rotation with security checks."""
        print(f"\n=== TOKEN ROTATION TEST ===")
        
        # Create initial token pair
        old_refresh, old_access = enhanced_token_manager.create_token_pair(
            user=self.user,
            device_info=self.device_info
        )
        
        old_jti = old_refresh['jti']
        old_family_id = old_refresh['family_id']
        
        # Rotate token
        new_refresh, new_access = enhanced_token_manager.rotate_token(
            refresh_token_str=str(old_refresh),
            device_info=self.device_info
        )
        
        # Verify rotation
        self.assertNotEqual(old_refresh['jti'], new_refresh['jti'])
        self.assertEqual(old_family_id, new_refresh['family_id'])  # Same family
        
        # Verify old token is blacklisted
        is_blacklisted = enhanced_token_manager.is_token_blacklisted(old_jti)
        self.assertTrue(is_blacklisted)
        
        print(f"✅ Token rotation successful")
        print(f"   Old JTI: {old_jti}")
        print(f"   New JTI: {new_refresh['jti']}")
        print(f"   Family ID maintained: {new_refresh['family_id']}")
        print(f"   Old token blacklisted: {is_blacklisted}")
        
        return new_refresh, new_access
    
    def test_token_blacklisting(self):
        """Test comprehensive token blacklisting."""
        print(f"\n=== TOKEN BLACKLISTING TEST ===")
        
        # Create token pair
        refresh_token, access_token = enhanced_token_manager.create_token_pair(
            user=self.user,
            device_info=self.device_info
        )
        
        token_jti = refresh_token['jti']
        
        # Test individual token blacklisting
        success = enhanced_token_manager.blacklist_token(
            token_jti=token_jti,
            reason=TokenSecurityReason.LOGOUT,
            user_id=self.user.id
        )
        
        self.assertTrue(success)
        
        # Verify token is blacklisted
        is_blacklisted = enhanced_token_manager.is_token_blacklisted(token_jti)
        self.assertTrue(is_blacklisted)
        
        print(f"✅ Individual token blacklisting successful")
        print(f"   Token JTI: {token_jti}")
        print(f"   Blacklisted: {is_blacklisted}")
        
        # Test mass blacklisting
        # Create multiple tokens
        tokens = []
        for i in range(3):
            refresh, access = enhanced_token_manager.create_token_pair(
                user=self.user,
                device_info=self.device_info
            )
            tokens.append(refresh)
        
        # Blacklist all user tokens
        blacklisted_count = enhanced_token_manager.blacklist_all_user_tokens(
            user_id=self.user.id,
            reason=TokenSecurityReason.ACCOUNT_COMPROMISE
        )
        
        self.assertGreaterEqual(blacklisted_count, 3)
        
        print(f"✅ Mass token blacklisting successful")
        print(f"   Tokens blacklisted: {blacklisted_count}")
        
        return blacklisted_count
    
    def test_session_management(self):
        """Test session management functionality."""
        print(f"\n=== SESSION MANAGEMENT TEST ===")
        
        # Create multiple sessions
        sessions = []
        for i in range(3):
            device_info = self.device_info.copy()
            device_info['device_id'] = f'device-{i}'
            
            refresh, access = enhanced_token_manager.create_token_pair(
                user=self.user,
                device_info=device_info
            )
            sessions.append(refresh)
        
        # Get active sessions
        active_sessions = enhanced_token_manager.get_user_active_sessions(self.user.id)
        
        self.assertGreaterEqual(len(active_sessions), 3)
        
        print(f"✅ Session management working")
        print(f"   Active sessions: {len(active_sessions)}")
        
        # Verify session data structure
        for session in active_sessions[:1]:  # Check first session
            required_fields = ['jti', 'created_at', 'expires_at', 'device_info', 'family_id']
            for field in required_fields:
                self.assertIn(field, session)
            
            print(f"   Session JTI: {session['jti']}")
            print(f"   Device ID: {session['device_info'].get('device_id')}")
        
        return active_sessions
    
    def test_security_checks(self):
        """Test security checks and suspicious activity detection."""
        print(f"\n=== SECURITY CHECKS TEST ===")
        
        # Create initial token
        refresh_token, access_token = enhanced_token_manager.create_token_pair(
            user=self.user,
            device_info=self.device_info
        )
        
        # Test normal rotation (should work)
        try:
            new_refresh, new_access = enhanced_token_manager.rotate_token(
                refresh_token_str=str(refresh_token),
                device_info=self.device_info
            )
            print(f"✅ Normal token rotation successful")
        except Exception as e:
            self.fail(f"Normal rotation should not fail: {e}")
        
        # Test device change detection
        suspicious_device = self.device_info.copy()
        suspicious_device.update({
            'user_agent': 'Different Browser',
            'ip_address': '********',
            'device_id': 'different-device'
        })
        
        # This should still work but might log warnings
        try:
            newer_refresh, newer_access = enhanced_token_manager.rotate_token(
                refresh_token_str=str(new_refresh),
                device_info=suspicious_device
            )
            print(f"✅ Device change handled appropriately")
        except Exception as e:
            print(f"⚠️ Device change triggered security measure: {e}")
        
        print(f"✅ Security checks functioning")
        
        return True
    
    def test_enhanced_auth_endpoints(self):
        """Test enhanced authentication endpoints."""
        print(f"\n=== ENHANCED AUTH ENDPOINTS TEST ===")
        
        # Test enhanced login
        login_data = {
            'username': 'token_test_user',
            'password': 'testpassword123',
            'device_id': 'test-device-123',
            'device_type': 'mobile',
            'app_version': '1.0.0',
            'platform': 'ios'
        }
        
        response = self.client.post('/api/auth/enhanced/login/', login_data)
        
        if response.status_code == 200:
            print(f"✅ Enhanced login successful")
            
            response_data = response.json()
            self.assertIn('access_token', response_data)
            self.assertIn('refresh_token', response_data)
            self.assertIn('session_info', response_data)
            
            access_token = response_data['access_token']
            refresh_token = response_data['refresh_token']
            
            print(f"   Session Family ID: {response_data['session_info']['family_id']}")
            
            # Test token refresh
            refresh_data = {
                'refresh_token': refresh_token,
                'device_id': 'test-device-123',
                'device_type': 'mobile'
            }
            
            refresh_response = self.client.post('/api/auth/enhanced/token/refresh/', refresh_data)
            
            if refresh_response.status_code == 200:
                print(f"✅ Enhanced token refresh successful")
                
                # Test logout
                self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
                logout_data = {'refresh_token': refresh_token}
                
                logout_response = self.client.post('/api/auth/enhanced/logout/', logout_data)
                
                if logout_response.status_code == 200:
                    print(f"✅ Enhanced logout successful")
                    return True
                else:
                    print(f"❌ Enhanced logout failed: {logout_response.status_code}")
            else:
                print(f"❌ Enhanced token refresh failed: {refresh_response.status_code}")
        else:
            print(f"❌ Enhanced login failed: {response.status_code}")
            if hasattr(response, 'json'):
                print(f"   Error: {response.json()}")
        
        return False
    
    def test_token_cleanup(self):
        """Test token cleanup functionality."""
        print(f"\n=== TOKEN CLEANUP TEST ===")
        
        # Create some tokens
        for i in range(5):
            enhanced_token_manager.create_token_pair(
                user=self.user,
                device_info=self.device_info
            )
        
        initial_count = OutstandingToken.objects.filter(user=self.user).count()
        print(f"   Initial token count: {initial_count}")
        
        # Run cleanup (won't clean much since tokens are fresh)
        cleaned_count = enhanced_token_manager.cleanup_expired_tokens()
        
        print(f"✅ Token cleanup executed")
        print(f"   Tokens cleaned: {cleaned_count}")
        
        return cleaned_count
    
    def test_comprehensive_token_management(self):
        """Run comprehensive token management tests."""
        print(f"\n=== COMPREHENSIVE TOKEN MANAGEMENT TEST ===")
        
        test_results = []
        
        # Run all tests
        tests = [
            ("Token Family Creation", self.test_token_family_creation),
            ("Enhanced Token Creation", self.test_enhanced_token_creation),
            ("Token Rotation", self.test_token_rotation),
            ("Token Blacklisting", self.test_token_blacklisting),
            ("Session Management", self.test_session_management),
            ("Security Checks", self.test_security_checks),
            ("Enhanced Auth Endpoints", self.test_enhanced_auth_endpoints),
            ("Token Cleanup", self.test_token_cleanup),
        ]
        
        for test_name, test_func in tests:
            try:
                result = test_func()
                success = result is not False and result is not None
                test_results.append((test_name, success))
                status = "✅ PASS" if success else "❌ FAIL"
                print(f"   {test_name}: {status}")
            except Exception as e:
                test_results.append((test_name, False))
                print(f"   {test_name}: ❌ ERROR - {e}")
        
        # Summary
        passed = sum(1 for _, result in test_results if result)
        total = len(test_results)
        
        print(f"\n=== TEST RESULTS SUMMARY ===")
        print(f"Passed: {passed}/{total} tests ({passed/total*100:.1f}%)")
        
        if passed == total:
            print("🎉 All enhanced token management tests passed!")
        elif passed >= total * 0.8:
            print("⚠️ Most tests passed - minor issues detected")
        else:
            print("❌ Significant issues detected in token management")
        
        return passed >= total * 0.8

def main():
    """Main test function."""
    print("🚀 Enhanced Token Management Test Suite")
    print("=" * 60)
    
    # Create test instance
    test_instance = EnhancedTokenManagementTest()
    test_instance.setUp()
    
    try:
        # Run comprehensive tests
        success = test_instance.test_comprehensive_token_management()
        
        print("\n" + "=" * 60)
        if success:
            print("✅ Enhanced Token Management System: READY FOR PRODUCTION")
        else:
            print("❌ Enhanced Token Management System: NEEDS ATTENTION")
        
        return success
        
    finally:
        test_instance.tearDown()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
