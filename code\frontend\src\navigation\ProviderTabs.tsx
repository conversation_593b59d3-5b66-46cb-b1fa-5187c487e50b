/**
 * Provider Tab Navigator
 * Bottom tab navigation for service provider users
 * Implements role-based tab structure
 */

import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons } from '@expo/vector-icons';

// Import provider screens
import { ProfileScreen } from '../screens/main/ProfileScreen';

// Import lazy-loaded provider screens
import { LazyProviderDashboardScreen } from '../components/lazy/LazyScreens';
import { LazyServiceManagementScreen } from '../components/lazy/LazyScreens';
import { LazyBookingManagementScreen } from '../components/lazy/LazyScreens';

// Import tab bar icon component
import { TabBarIcon } from '../components/navigation/TabBarIcon';

// Navigation types
export type ProviderTabParamList = {
  Dashboard: undefined;
  Services: undefined;
  Bookings: undefined;
  Profile: undefined;
};

const Tab = createBottomTabNavigator<ProviderTabParamList>();

export const ProviderTabs: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          switch (route.name) {
            case 'Dashboard':
              iconName = focused ? 'analytics' : 'analytics-outline';
              break;
            case 'Services':
              iconName = focused ? 'briefcase' : 'briefcase-outline';
              break;
            case 'Bookings':
              iconName = focused ? 'calendar' : 'calendar-outline';
              break;
            case 'Profile':
              iconName = focused ? 'person' : 'person-outline';
              break;
            default:
              iconName = 'help-outline';
          }

          return <TabBarIcon name={iconName} color={color} size={size} />;
        },
        tabBarActiveTintColor: '#007AFF',
        tabBarInactiveTintColor: '#8E8E93',
        tabBarStyle: {
          backgroundColor: '#FFFFFF',
          borderTopWidth: 1,
          borderTopColor: '#E5E5EA',
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
        },
      })}
    >
      <Tab.Screen
        name="Dashboard"
        component={LazyProviderDashboardScreen}
        options={{
          title: 'Dashboard',
        }}
      />
      <Tab.Screen
        name="Services"
        component={LazyServiceManagementScreen}
        options={{
          title: 'Services',
        }}
      />
      <Tab.Screen
        name="Bookings"
        component={LazyBookingManagementScreen}
        options={{
          title: 'Bookings',
        }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileScreen}
        options={{
          title: 'Profile',
        }}
      />
    </Tab.Navigator>
  );
};
