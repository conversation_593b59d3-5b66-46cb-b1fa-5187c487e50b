/**
 * Provider Stack Navigator
 * Role-based navigation stack for service provider users
 * Implements lazy loading and FSM-based navigation patterns
 */

import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';

// Import provider-specific screens
import { ProfileScreen } from '../screens/main/ProfileScreen';
import { SettingsScreen } from '../screens/main/SettingsScreen';

// Import lazy-loaded provider screens
import { LazyServiceManagementScreen } from '../components/lazy/LazyScreens';
import { LazyBookingManagementScreen } from '../components/lazy/LazyScreens';
import { LazyProviderAnalyticsScreen } from '../components/lazy/LazyScreens';
import { LazyCustomerManagementScreen } from '../components/lazy/LazyScreens';
import { LazyEditProfileScreen } from '../components/lazy/LazyScreens';
import { LazyNotificationsScreen } from '../components/lazy/LazyScreens';

// Import provider tabs
import { ProviderTabs } from './ProviderTabs';

// Navigation types
export type ProviderStackParamList = {
  ProviderTabs: undefined;
  ServiceManagement: undefined;
  BookingManagement: undefined;
  ProviderAnalytics: undefined;
  CustomerManagement: undefined;
  EditProfile: undefined;
  Notifications: undefined;
  Settings: undefined;
};

const Stack = createStackNavigator<ProviderStackParamList>();

export const ProviderStack: React.FC = () => {
  return (
    <Stack.Navigator
      initialRouteName="ProviderTabs"
      screenOptions={{
        headerShown: false,
        cardStyle: { backgroundColor: '#FFFFFF' },
        animationEnabled: true,
        presentation: 'card',
      }}
    >
      {/* Main provider tabs */}
      <Stack.Screen
        name="ProviderTabs"
        component={ProviderTabs}
        options={{
          headerShown: false,
        }}
      />

      {/* Service management screen */}
      <Stack.Screen
        name="ServiceManagement"
        component={LazyServiceManagementScreen}
        options={{
          headerShown: true,
          title: 'Service Management',
          headerBackTitleVisible: false,
        }}
      />

      {/* Booking management screen */}
      <Stack.Screen
        name="BookingManagement"
        component={LazyBookingManagementScreen}
        options={{
          headerShown: true,
          title: 'Booking Management',
          headerBackTitleVisible: false,
        }}
      />

      {/* Provider analytics screen */}
      <Stack.Screen
        name="ProviderAnalytics"
        component={LazyProviderAnalyticsScreen}
        options={{
          headerShown: true,
          title: 'Analytics',
          headerBackTitleVisible: false,
        }}
      />

      {/* Customer management screen */}
      <Stack.Screen
        name="CustomerManagement"
        component={LazyCustomerManagementScreen}
        options={{
          headerShown: true,
          title: 'Customer Management',
          headerBackTitleVisible: false,
        }}
      />

      {/* Edit profile screen */}
      <Stack.Screen
        name="EditProfile"
        component={LazyEditProfileScreen}
        options={{
          headerShown: true,
          title: 'Edit Profile',
          headerBackTitleVisible: false,
        }}
      />

      {/* Notifications screen */}
      <Stack.Screen
        name="Notifications"
        component={LazyNotificationsScreen}
        options={{
          headerShown: true,
          title: 'Notifications',
          headerBackTitleVisible: false,
        }}
      />

      {/* Settings screen */}
      <Stack.Screen
        name="Settings"
        component={SettingsScreen}
        options={{
          headerShown: true,
          title: 'Settings',
          headerBackTitleVisible: false,
        }}
      />
    </Stack.Navigator>
  );
};
