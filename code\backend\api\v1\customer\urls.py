"""
Customer API v1 URLs
Customer-specific endpoints for the mobile app
"""

from django.urls import path, include
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework import status

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def customer_dashboard(request):
    """Customer dashboard endpoint"""
    if not request.user.is_customer:
        return Response(
            {'error': 'Access denied. Customer role required.'},
            status=status.HTTP_403_FORBIDDEN
        )
    
    return Response({
        'message': 'Customer dashboard',
        'user': request.user.get_full_name(),
        'role': request.user.role,
        'endpoints': {
            'profile': '/api/v1/customer/profile/',
            'bookings': '/api/v1/customer/bookings/',
            'search': '/api/v1/customer/search/',
        }
    })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def customer_profile(request):
    """Customer profile endpoint"""
    if not request.user.is_customer:
        return Response(
            {'error': 'Access denied. Customer role required.'},
            status=status.HTTP_403_FORBIDDEN
        )
    
    # Redirect to existing profile endpoint for now
    from authentication.views import UserProfileView
    view = UserProfileView()
    view.request = request
    return view.get(request)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def customer_bookings(request):
    """Customer bookings endpoint"""
    if not request.user.is_customer:
        return Response(
            {'error': 'Access denied. Customer role required.'},
            status=status.HTTP_403_FORBIDDEN
        )
    
    return Response({
        'message': 'Customer bookings',
        'bookings': [],  # TODO: Implement bookings logic
        'count': 0
    })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def customer_search(request):
    """Customer search endpoint"""
    if not request.user.is_customer:
        return Response(
            {'error': 'Access denied. Customer role required.'},
            status=status.HTTP_403_FORBIDDEN
        )
    
    return Response({
        'message': 'Customer search',
        'results': [],  # TODO: Implement search logic
        'filters': {}
    })

@api_view(['GET'])
@permission_classes([AllowAny])
def customer_root(request):
    """Customer API root endpoint"""
    return Response({
        'message': 'Customer API v1',
        'endpoints': {
            'dashboard': '/api/v1/customer/dashboard/',
            'profile': '/api/v1/customer/profile/',
            'bookings': '/api/v1/customer/bookings/',
            'search': '/api/v1/customer/search/',
        }
    })

urlpatterns = [
    # Customer root
    path('', customer_root, name='customer_root'),

    # Customer-specific endpoints
    path('dashboard/', customer_dashboard, name='customer_dashboard'),
    path('profile/', customer_profile, name='customer_profile'),
    path('bookings/', customer_bookings, name='customer_bookings'),
    path('search/', customer_search, name='customer_search'),
]
